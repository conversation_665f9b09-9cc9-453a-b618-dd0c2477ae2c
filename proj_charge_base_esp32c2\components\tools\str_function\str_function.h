#ifndef _STR_FUNCTION_H_
#define _STR_FUNCTION_H_

#include <stdint.h>

#ifdef __cplusplus
 extern "C" {
#endif

int32_t string_spilit(char *str, uint32_t len, char **cmdout, uint32_t cmdmax);
int32_t string_spilit_static(char *str, uint32_t len, char **cmdout, uint32_t cmdmax);
int32_t version_string_to_numeric(const char *str_ver, uint32_t *value_ver);
int32_t version_numeric_to_string(uint32_t value_ver, char *str_ver);
 
#ifdef __cplusplus
 }
#endif

#endif
