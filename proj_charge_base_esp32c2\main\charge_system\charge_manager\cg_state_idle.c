#include "logger.h"
#include "local_device.h"
#include "charge_manager.h"
#include "drv_ina226.h"
#include "drv_husb238.h"

#include "pixeleff_manager.h"
#include "pixel_common.h"
#include "pixeleff_system.h"
#define tag  "[CG-State-IDLE]"

static uint16_t voltage[6];
static float current[6];
void state_cg_idle_prev_process()
{
	mlog_i(tag, "Enter IDLE state");
	// 设置为白色LED，表示空闲状态
	pixeleff_system_colour(WHITE, 0.5);
	uint8_t count = drv_husb238_getcapabilities(voltage, current);
	if(count > 0)
	{
		drv_husb238_selvoltage(PDO_5V);
		for (int i = 0; i < count; i++)
		{
			mlog_i(tag, "voltage: %d, current: %f", voltage[i], current[i]);
		}
	}
}

void state_cg_idle_curr_process()
{
	// 检测功率，判断是否有设备连接
	float power = ina226_get_power();

	// 如果功率大于100mW，说明有设备连接，切换到充电状态
	if (power >= 100)
	{
		mlog_i(tag, "Device connected, power: %.2f mW", power);
		charge_manager_transto(STA_CG_CHARGING);
	}
}

void state_cg_idle_post_process()
{
	mlog_i(tag, "Exit IDLE state");
}
