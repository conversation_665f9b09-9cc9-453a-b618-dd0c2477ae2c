#include "logger.h"
#include "local_device.h"
#include "charge_manager.h"
#include "drv_ina226.h"
#include "drv_husb238.h"

#include "pixeleff_manager.h"
#include "pixel_common.h"
#include "pixeleff_system.h"
#define tag  "[CG-State-IDLE]"

static float entertime = 0;

void state_cg_idle_prev_process()
{
	mlog_i(tag, "");
}

void state_cg_idle_curr_process()
{

}

void state_cg_idle_post_process()
{

}
