#ifndef _PROTOCOL_TLVC_DECODER_H_
#define _PROTOCOL_TLVC_DECODER_H_

#include <stdint.h>
#include <string.h>
#include "generate_iv.h"

#define ENCRYPT_HEAD_A          0xAA
#define ENCRYPT_HEAD_B          0xBB
#define ENCRYPT_PROTOCOL_LENGTH  19
#define ENCRYPT_IV_LENGTH        12
#define ENCRYPT_KEY_LENGTH       12

typedef enum {
    DECRYPT_STATE_HEADER_A,
    DECRYPT_STATE_HEADER_B,
    DECRYPT_STATE_IV,
    DECRYPT_STATE_LENGTH_LSB,
    DECRYPT_STATE_LENGTH_MSB,
    DECRYPT_STATE_CRC8,
    DECRYPT_STATE_DATA,
    DECRYPT_STATE_CRC16_LSB,
    DECRYPT_STATE_CRC16_MSB,
    DECRYPT_STATE_COMPLETE,
	DECRYPT_STATE_MAX
} decrypt_state_e;

typedef void (*pfunc_tlv_callback)(uint8_t*, uint32_t);

typedef struct
{
	// TAG
	char 		*tag;
	uint32_t  	taglen;	
	pfunc_tlv_callback pfunc;
} element_tlvc_t;

typedef struct
{
	uint8_t  		*data;
	uint32_t  		bytes;
	uint32_t  		length;
} data_buffer_t;

typedef struct
{
	uint32_t 		state;
	uint8_t 		head0;
	uint8_t 		head1;
	uint8_t 		len_h;
	uint8_t 		len_l;
	uint8_t			index;

	uint8_t 		crc8;
	uint16_t 		crc16;

	uint16_t  		total_len;
	uint16_t  		value_len;
		
	data_buffer_t   buffer;	

	uint32_t 		elem_num;
	element_tlvc_t  *elems;	
} protocol_tlvc_t;

typedef int32_t (*pfunc_encrypt_generate_iv)(uint8_t *, uint8_t *);
typedef int32_t (*pfunc_decrypt_callback)(protocol_tlvc_t *, uint8_t *, uint32_t);

typedef struct {
    decrypt_state_e state;
	uint8_t iv_length;
    uint8_t iv[ENCRYPT_IV_LENGTH];
	uint8_t decrypt_iv_length;
    uint8_t decrypt_iv[ENCRYPT_IV_LENGTH];
	uint8_t key_length;
    uint8_t key[ENCRYPT_KEY_LENGTH];
	uint16_t data_length;
    uint16_t received_length;
    uint8_t crc8_value;
    uint16_t crc16_value;
	pfunc_encrypt_generate_iv generate_iv;
    pfunc_decrypt_callback decrypt_callback;
} data_encrypt_t;


#ifdef __cplusplus
 extern "C" {
#endif


int32_t protocol_tlvc_regist(protocol_tlvc_t *tlvc, 
							const char head0,
							const char head1,
							element_tlvc_t elems[], 
							uint32_t elemsize, 
							uint8_t* buf, 
							uint32_t buflen);

int32_t protocol_tlvc_regist_encrypt(protocol_tlvc_t *tlvc, 
								const char head0,
								const char head1,
								element_tlvc_t elems[], 
								uint32_t elemsize, 
								uint8_t* buf, 
								uint32_t buflen,
								data_encrypt_t  *encrypt,
								uint8_t *key, 
								uint8_t keylen,
							    pfunc_encrypt_generate_iv pfunc_iv);

int32_t protocol_tlvc_decode(protocol_tlvc_t *tlvc, uint8_t *data, uint32_t len);

int32_t protocol_tlvc_decrypt(protocol_tlvc_t *tlvc, data_encrypt_t  *encrypt, uint8_t *data, uint32_t len);

int32_t protocol_tlvc_pack(const char head0,
							const char head1,
							const char *tag,
							uint32_t taglen,
							uint8_t *value,
							uint32_t valuelen,
							uint8_t *data,
							uint32_t datalen);

int32_t protocol_tlvc_pack_encrypt(const char head0,
									const char head1,
									const char *tag,
									uint32_t taglen,
									uint8_t *value,
									uint32_t valuelen,
									uint8_t *data,
									uint32_t datalen,
								    data_encrypt_t  *encrypt);

int32_t protocol_tlvc_regist_value(protocol_tlvc_t *tlvc, 
							const char head0,
							const char head1,
							uint8_t* buf, 
							uint32_t buflen);

int32_t protocol_tlvc_decode_value(protocol_tlvc_t *tlvc,
									uint8_t *din, 
									uint32_t lin, 
									uint8_t *dout, 
									uint32_t lout);

int32_t protocol_tlvc_decode_tag(protocol_tlvc_t *tlvc,
									uint8_t *din,
									uint32_t lin,
									char *tag,
									uint32_t taglen,
									uint8_t *dout,
									uint32_t *lout);

#ifdef __cplusplus
 }
#endif

#endif
