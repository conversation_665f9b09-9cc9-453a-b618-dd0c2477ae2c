#include <string.h>
#include "i2c_device.h"
#include "local_device.h"
#include "logger.h"
#include "protocol_tlvc.h"
#include "drv_ina226.h"
#include "drv_husb238.h"

#define tag     "[i2c0 device]"

typedef int32_t (*pfunc_init)(void);

typedef enum
{
    I2C0_DEV_QCHARGE = 0,
    I2C0_DEV_GALV,
    I2C0_DEV_MAX
} e_device_list;

typedef struct 
{
    const char*     name;
    uint8_t         dev_enum;
    uint8_t         address;
    int8_t          state;
    pfunc_init      dev_init;
} i2c0_device_t;

static int32_t pa_init(void);
static int32_t galv_init(void);


static int32_t trig_close_chargeout = 0;

static i2c0_device_t dev_list[] = 
{
    {"QCHARGE",    I2C0_DEV_QCHARGE,     0x10, -1,  pa_init},
    {"GALV",  I2C0_DEV_GALV,   0x80, -1,  galv_init},
};

static int32_t pa_init(void)
{
    return 0;
}

static int32_t galv_init(void)
{
    ina226_init();
    return 0;
}


static int32_t detect(uint8_t addr)
{
    esp_err_t ret = ESP_OK;
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();  

    i2c_master_start(cmd); 
    i2c_master_write_byte(cmd, addr | I2C_MASTER_WRITE, true);  
    i2c_master_stop(cmd);  
    ret = i2c_master_cmd_begin(I2C_NUM_0, cmd, 50);
    i2c_cmd_link_delete(cmd);

    return (ret == ESP_OK) ? 0 : -1;
}

static void charge_process()
{    
    uint16_t PD_Voltage;
    float PD_Current;
    HUSB238_Capability_t PDCapabilities[6];
    uint16_t vol= drv_husb238_getcapabilities(&PD_Voltage, &PD_Current);
    int kk=drv_husb238_extractcap(PDCapabilities);
    mlog_i(tag,"num:%d",kk);
    for(int i = 0; i < kk; i++)
    {
        if(PDCapabilities[i].detected == true)
        {
            mlog_i(tag, "| V: %dV | : %.2fA", PDCapabilities[i].voltage, PDCapabilities[i].current);
        }
    }
   // drv_husb238_selvoltage(PDO_5V);
}

static void i2c_task(void *pvParameters)
{
    dev_charge()->time.rtos_delayms(10);

    mlog_i(tag, "start detect ...");
    uint32_t devnum = sizeof(dev_list) / sizeof (i2c0_device_t);
    for (uint32_t i = 0; i < devnum; i++) 
    {
        if(detect(dev_list[i].address) != 0) 
        {
            dev_list[i].state = -1;
            mlog_w(tag, "[%d/%d], name:[%s] not found.", 
                i, devnum, dev_list[i].name);
        }
        else
        {
            dev_list[i].state = 0;
            mlog_i(tag, "[%d/%d], name:[%s] found !", 
                i, devnum, dev_list[i].name);
        }
        dev_charge()->time.rtos_delayms(1);
    }
    mlog_i(tag, "finish detect");
    for (uint32_t i = 0; i < devnum; i++) 
    {        
        dev_list[i].dev_init();
    }
    charge_process();
    while (1)
    {
        ina226_update();
        dev_charge()->time.rtos_delayms(500);
    }
}
 
int32_t i2c_device_init()
{
    xTaskCreate(&i2c_task, "i2c_task", 4096, NULL, 5, NULL);
    return 0;
}

