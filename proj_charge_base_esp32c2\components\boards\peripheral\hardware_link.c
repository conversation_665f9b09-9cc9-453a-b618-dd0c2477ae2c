#include "hardware_link.h"
#include <string.h>

#include "bsp_timestamp.h"
#include "bsp_nvs.h"
#include "bsp_uart0_commander.h"
#include "bsp_chip_id.h"
#include "bsp_i2c0_bus.h"
#include "bsp_gpio_ctrl.h"
#include "bsp_gpio_detect.h"
#include "bsp_pixel_led_onboard.h"


int32_t board_drv_link_chip_id(dev_chip_id_t *dev)
{
    dev->init   = bsp_chip_id_init;
    dev->get    = bsp_chip_id_get;
    return dev->init();
}

int32_t board_drv_link_timestamp(dev_timestamp_t *dev)
{
    dev->init           = bsp_timestamp_init;
    dev->get_micros     = bsp_get_micros;
    dev->get_millis     = bsp_get_millis;
    dev->get_seconds    = bsp_get_second;
    dev->block_delayus  = bsp_block_delayus;
    dev->block_delayms  = bsp_block_delayms;
    dev->rtos_delayms   = bsp_rtos_delayms;
    return dev->init();
}

int32_t board_drv_link_nvs(dev_nvs_t *dev)
{
    dev->init       = bsp_nvs_init;
    dev->set_str    = bsp_nvs_set_str;
    dev->get_str    = bsp_nvs_get_str;
    dev->set_u32    = bsp_nvs_set_u32;
    dev->get_u32    = bsp_nvs_get_u32;
    dev->set_i8     = bsp_nvs_set_i8;
    dev->get_i8     = bsp_nvs_get_i8;
    dev->set_u8     = bsp_nvs_set_u8;
    dev->get_u8     = bsp_nvs_get_u8;
    dev->set_i16    = bsp_nvs_set_i16;
    dev->get_i16    = bsp_nvs_get_i16;
    dev->set_u16    = bsp_nvs_set_u16;
    dev->get_u16    = bsp_nvs_get_u16;
    dev->set_blob   = bsp_nvs_set_blob;
    dev->get_blob   = bsp_nvs_get_blob;
    return dev->init();
}

int32_t board_drv_link_comport0(dev_comport_t *dev)
{
    dev->init   = bsp_uart0_commander_init;
    dev->read   = bsp_uart0_commander_receive;
    dev->write  = bsp_uart0_commander_transmit;
    return dev->init();
}

int32_t board_drv_link_i2c0_bus(dev_i2c_bus_t *dev)
{
    dev->init   = bsp_i2c0_bus_init;
    return dev->init();
}

int32_t board_drv_link_vbus_ctrl(dev_gpio_ctrl_t *dev)
{
    dev->init       = bsp_gpio_out_power_init;
    dev->set_on     = bsp_gpio_out_power_on;
    dev->set_off    = bsp_gpio_out_power_off;
    return dev->init();
}

int32_t board_drv_link_pixel_onboard(dev_pixel_led_t *dev)
{
    dev->init   = bsp_pixel_led_onboard_init;
    dev->set    = bsp_pixel_led_onboard_set;
    dev->flush  = bsp_pixel_led_onboard_flush;
    dev->clear  = bsp_pixel_led_onboard_clear;
    return dev->init();
}

int32_t board_drv_link_adc(dev_adc_t *dev)
{
    dev->init   = bsp_adc_init;
    dev->get_vol = bsp_gpio_v_de_state;
    return dev->init();
}

