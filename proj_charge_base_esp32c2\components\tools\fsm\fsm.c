#include "fsm.h"

void fsm_init(fsm_t * fsm)
{	
	fsm->state_curr = -1;
	fsm->state_next = -1;
	fsm->state_last = -1;
	
	for (uint8_t i = 0; i < MAX_STA_NUM; i++)
	{
		fsm->prev_process[i] = NULL;
		fsm->curr_process[i] = NULL;
		fsm->post_process[i] = NULL;
	}
}

void fsm_regist_state(fsm_t * fsm, 
					  uint8_t sta_enum,	// state enum, 0-max state
					  p_process prev,	// function, or NULL
					  p_process curr,
					  p_process post)
{
	if (sta_enum < MAX_STA_NUM)
	{
		fsm->prev_process[sta_enum] = prev;
		fsm->curr_process[sta_enum] = curr;
		fsm->post_process[sta_enum] = post;
	}
}

void fsm_trans_to(fsm_t * fsm, uint8_t state)
{
	if (state < MAX_STA_NUM)
	{
		fsm->state_next = state;
	}
}

int8_t fsm_get_curr_state(fsm_t * fsm)
{
	return fsm->state_curr;
}

int8_t fsm_get_next_state(fsm_t * fsm)
{
	return fsm->state_next;
}

int8_t fsm_get_last_state(fsm_t * fsm)
{
	return fsm->state_last;
}

void fsm_update(fsm_t * fsm)
{
	// last post process
	if ((fsm->state_last != fsm->state_next)
	 && (fsm->state_curr >= 0))
	{
		if (fsm->post_process[fsm->state_curr] != NULL)
		{
			fsm->post_process[fsm->state_curr]();
		}
	}
	
	// prev process
	if ((fsm->state_next != fsm->state_curr)
	 && (fsm->state_next >= 0))
	{		
		fsm->state_curr = fsm->state_next;
		if (fsm->prev_process[fsm->state_next] != NULL)
		{
			fsm->prev_process[fsm->state_curr]();
		}
	}
	
	// curr process
	if ((fsm->state_curr >= 0)
	 && (fsm->curr_process[fsm->state_curr] != NULL))
	{
		fsm->curr_process[fsm->state_curr]();
	}
	
	// post process
	if ((fsm->state_next != fsm->state_curr)
	 && (fsm->state_curr >= 0)		
	 && (fsm->post_process[fsm->state_curr] != NULL))
	{
		fsm->post_process[fsm->state_curr]();
	}
	
	// record last jump
	fsm->state_last = fsm->state_next;
}



 
