#include <stdio.h>
#include <math.h>
#include "pcm_convert.h"


void mono_float_to_pcm16(float* in, int16_t* out, size_t len) 
{
    for(int i=0; i<len; i++) 
    {
        float sample = in[i];
        sample = fmaxf(-1.0f, fminf(sample, 1.0f)); // 限幅
        out[i] = (int16_t)(sample * 32767.0f);
    }
}

void mono_pcm16_to_float(uint8_t* pcm, float* out, size_t len_bytes) 
{
    const size_t num_samples = len_bytes / 2;
    const float scale = 1.0f / 32768.0f;

    for(size_t i = 0; i < num_samples; ++i) 
    {
        // 单声道处理
        int16_t l = (int16_t)(pcm[2*i + 1] << 8) | pcm[2*i];
        out[i] = fmaxf(-1.0f, fminf(l * scale, 1.0f));
    }
}

void stereo_pcm16_to_float( uint8_t* pcm,
                            float* left,
                            float* right,
                            size_t len_bytes) 
{
    const size_t num_samples = len_bytes / 4;
    const float scale = 1.0f / 32768.0f;

    for(size_t i = 0; i < num_samples; ++i) 
    {
        // 左声道
        int16_t l = (int16_t)(pcm[4*i + 1] << 8) | pcm[4*i];
        left[i] = fmaxf(-1.0f, fminf(l * scale, 1.0f));

        // 右声道
        int16_t r = (int16_t)(pcm[4*i + 3] << 8) | pcm[4*i + 2];
        right[i] = fmaxf(-1.0f, fminf(r * scale, 1.0f));
    }
}

void stereo_pcm16_spilit( uint8_t* pcm,
                          int16_t* left,
                          int16_t* right,
                          size_t len_bytes) 
{
    const size_t num_samples = len_bytes / 4;

    for(size_t i = 0; i < num_samples; ++i) 
    {
        // 左声道
        left[i] = (int16_t)(pcm[4*i + 1] << 8) | pcm[4*i];

        // 右声道
        right[i] = (int16_t)(pcm[4*i + 3] << 8) | pcm[4*i + 2];
    }
}

void stereo_pcm16_merge( int16_t* left, 
                         int16_t* right,
                         size_t samples, 
                         uint8_t* stereo_out) 
{
    for (size_t i = 0; i < samples; i++) 
    {
        // 将左声道样本写入缓冲区
        stereo_out[i * 4] = (uint8_t)(left[i] & 0xFF);
        stereo_out[i * 4 + 1] = (uint8_t)((left[i] >> 8) & 0xFF);

        // 将右声道样本写入缓冲区
        stereo_out[i * 4 + 2] = (uint8_t)(right[i] & 0xFF);
        stereo_out[i * 4 + 3] = (uint8_t)((right[i] >> 8) & 0xFF);
    }
}

