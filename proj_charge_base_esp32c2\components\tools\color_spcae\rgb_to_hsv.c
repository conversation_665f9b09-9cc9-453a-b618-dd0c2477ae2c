#include "rgb_to_hsv.h"

#ifndef MIN
#define MIN(a,b) (((a)<(b))?(a):(b))
#endif

#define R (0)
#define G (1)
#define B (2)

void find_min_max(float r, float g, float b, float *min, float *max, uint8_t *min_i, uint8_t *max_i)
{
	float val[3];
	val[0] = r;
	val[1] = g;
	val[2] = b;
	uint8_t min_idx = 0;
	uint8_t max_idx = 0;
	float max_val = 0;
	float min_val = 9999.0f;
	for (uint8_t i = 0; i < 3; i++)
	{
		if (val[i] > max_val)
		{
			max_val = val[i];
			max_idx = i;
		}
		if (val[i] < min_val)
		{
			min_val = val[i];
			min_idx = i;
		}
	}
	*min = min_val;
	*max = max_val;
	*min_i = min_idx;
	*max_i = max_idx;
}

void rgb_to_hsv(float r, float g, float b, float *h, float *s, float *v)
{
	float min_val, max_val;
	uint8_t min_index = 0;
	uint8_t max_index = 0;

	find_min_max(r, g, b, &min_val, &max_val, &min_index, &max_index);

    *s = (max_val < 0.0001f) ? 0 : (max_val - min_val) / max_val;
    *v = max_val;

    /* Saturation is 0 */
    if((*s * 100.0f) < 0.1f)
    {
        /* Hue is undefined, monochrome */
        *h = 0;
        return;
    }
    else if(max_index == 0)
    {
        if(min_index == G)
        {
            /* H = 5 + B' */
            *h = 5 + (max_val - b) / (max_val - min_val);
        }
        else
        {
            /* H = 1 - G' */
            *h = 1 - (max_val - g) / (max_val - min_val);
        }
    }
    else if(max_index == G)
    {
        if(min_index == B)
        {
            /* H = 1 + R'*/
            *h = 1 + (max_val - r) / (max_val - min_val);
        }
        else
        {
            /* H = 3 - B' */
            *h = 3 - (max_val - b) / (max_val - min_val);
        }
    }
    /*  This is actually a problem with the original paper,
		I've fixed it here, should email them... */
    else if(max_index == B && min_index == R)
    {
        /* H = 3 + G' */
        *h = 3 + (max_val - g) / (max_val - min_val);
    }
    else
    {
        /* H = 5 - R' */
        *h = 5 - (max_val - r) / (max_val - min_val);
    }

    *h = MIN(*h / 6.0f, 1.0f);
}



