#ifndef _CRC_H_
#define _CRC_H_

#include <stdint.h>

#ifdef __cplusplus
 extern "C" {
#endif

uint64_t crc64(uint8_t *buf, uint32_t len);
uint64_t crc64_at(uint64_t init, uint8_t *buf, uint32_t len);
uint32_t crc32(uint8_t *buf, uint32_t len);
uint32_t crc32_at(uint32_t init, uint8_t *buf, uint32_t len);
uint16_t crc16(uint8_t *buf, uint16_t len);
uint16_t crc16_at(uint16_t init, uint8_t *buf, uint16_t len);
uint8_t crc8(uint8_t *buf, uint8_t len);
uint8_t crc8_at(uint8_t init, uint8_t *buf, uint8_t len);
 
#ifdef __cplusplus
 }
#endif

#endif
