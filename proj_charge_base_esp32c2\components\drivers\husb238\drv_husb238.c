#include <string.h>
#include "drv_husb238.h"
#include "driver/i2c.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"

bool initlized = false;
// 电压值
uint16_t votlage_Table[] = {0, 5, 9, 12, 15, 18, 20};
// 电流值
float current_Table[] = {0.5f, 0.7f, 1.0f, 1.25f, 1.5f, 1.75f, 2.0f, 2.25f, 2.5f, 2.75f, 3.0f, 3.25f, 3.5f, 4.0f, 4.5f, 5.0f};

#define I2C_NUM I2C_NUM_0    /*!< I2C port number for master dev */
#define I2C_TX_BUF_DISABLE 0 /*!< I2C master do not need buffer */
#define I2C_RX_BUF_DISABLE 0 /*!< I2C master do not need buffer */
#define I2C_FREQ_HZ 100000   // 400000   /*!< I2C master clock frequency */

#define WRITE_BIT I2C_MASTER_WRITE /*!< I2C master write */
#define READ_BIT I2C_MASTER_READ   /*!< I2C master read */
#define ACK_CHECK_EN 1             /*!< I2C master will check ack from slave*/
#define ACK_CHECK_DIS 0            /*!< I2C master will not check ack from slave */
#define ACK_VAL (i2c_ack_type_t)0  /*!< I2C ack value */
#define NACK_VAL (i2c_ack_type_t)1 /*!< I2C nack value */

#define DEBUG_LOG 0 /*是否日志输出*/
// I2C 读取多个寄存器值
int drv_husb238_i2creadbytes(uint8_t slaveAddr, uint8_t startAddress, uint16_t nBytesRead, uint8_t *data)
{
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, slaveAddr << 1 | WRITE_BIT, ACK_CHECK_EN);
    i2c_master_write_byte(cmd, startAddress, ACK_CHECK_EN);

    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, slaveAddr << 1 | READ_BIT, ACK_CHECK_EN);
    if (nBytesRead > 1)
        i2c_master_read(cmd, data, nBytesRead - 1, ACK_VAL);
    i2c_master_read_byte(cmd, data + nBytesRead - 1, NACK_VAL);
    i2c_master_stop(cmd);

    int ret = i2c_master_cmd_begin(I2C_NUM, cmd, 1000 / portTICK_PERIOD_MS);
    i2c_cmd_link_delete(cmd);
    return ret;
}

// I2C写入寄存器值
int drv_husb238_writereg(uint8_t slaveAddr, uint8_t writeAddress, uint8_t data)
{
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();

    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (slaveAddr << 1) | WRITE_BIT, ACK_CHECK_EN);
    i2c_master_write_byte(cmd, writeAddress, ACK_CHECK_EN);

    i2c_master_write_byte(cmd, data, ACK_CHECK_EN);

    i2c_master_stop(cmd);

    esp_err_t ret = i2c_master_cmd_begin(I2C_NUM, cmd, 1000 / portTICK_PERIOD_MS);
    i2c_cmd_link_delete(cmd);

    static uint8_t dataCheck;
    drv_husb238_i2creadbytes(slaveAddr, writeAddress, 1, &dataCheck);
    if (dataCheck != data)
        return -2;

    return ret;
}

// 发送Get_SRC_Cap命令
void drv_husb238_refreshsrccap()
{
    uint8_t cmd = Get_SRC_Cap;
    drv_husb238_writereg(HUSB238_I2CAddress, Reg_GO_COMMAND, cmd);
}

// 读取 HUSB238 全部寄存器数据
void drv_husb238_readallreg(uint8_t *regs)
{
    memset(regs, 0, 10);
    drv_husb238_i2creadbytes(HUSB238_I2CAddress, 0x00, 10, (uint8_t *)regs);
    if(DEBUG_LOG){
        printf("\nHUSB238_ReadAllReg:\n");
        for(int i = 0; i < 10; i++){
            printf("0x%02x: 0x%02x \n", i, regs[i]);
        }
        printf("\n");
    }    
}

// regs 寄存器值 转化可输出能力列表
int drv_husb238_extractcap(HUSB238_Capability_t *pdoList)
{
    uint8_t regs[10];
    int detected_count = 0;

    if (!pdoList)
    {
        return 0;
    }
    drv_husb238_readallreg(regs);
    HUSB238_Reg_SRC_PDO *reg;
    
    for (int i = 0; i < 6; i++)
    {
        reg = (HUSB238_Reg_SRC_PDO *)(regs + i + 2);
        if (reg->bit.SRC_DETECTED){
            pdoList[detected_count].detected = true;
            pdoList[detected_count].current = to_current(reg->bit.SRC_CURRENT);
            pdoList[detected_count].voltage = drv_husb238_voltage2pdo((HUSB238_Voltage_e)(i + 1));
            detected_count++;
        }
    }

    if(DEBUG_LOG && detected_count > 0){
        printf("\nSupported PDO List: (Detected %d PDOs)\n", detected_count);
        for(int i = 0; i < detected_count; i++){
            printf("| Voltage: %dV | Current: %.2fA\n", pdoList[i].voltage, pdoList[i].current);
        }
        printf("\n");
    }
    
    return detected_count;
}


// 获取当前PD输出能力
uint16_t drv_husb238_getcapabilities(uint16_t *voltage, float *current)
{
    HUSB238_Voltage_e voltage_e;
    HUSB238_CURRENT_e current_e;
    uint8_t regs[6];
    int detected_count = 0;

    drv_husb238_refreshsrccap();
    vTaskDelay( 500 / portTICK_PERIOD_MS );
    
    // 获取当前激活的电压和电流
    drv_husb238_getcurrentmode(&voltage_e, &current_e);
    *voltage = drv_husb238_voltage2pdo(voltage_e);
    if(*voltage != 0){
        *current = to_current(current_e);
    }else{
        *current = 0;
    }
    
    // 计算检测到的PDO能力数量
    drv_husb238_readallreg(regs);
    HUSB238_Reg_SRC_PDO *reg;
    
    for (int i = 0; i < 6; i++)
    {
        reg = (HUSB238_Reg_SRC_PDO *)(regs + i + 2);
        if (reg->bit.SRC_DETECTED){
            detected_count++;
        }
    }
    
    if(DEBUG_LOG){
        printf("\nCurrent PDO Capability:\nVoltage: %dV\nCurrent: %.2fA\nDetected PDO Count: %d\n", *voltage, *current, detected_count);
    }

    return (uint16_t)detected_count;
}

// 请求PDO输出
void drv_husb238_requestpdo()
{
    uint8_t cmd = Request_PDO;
    drv_husb238_writereg(HUSB238_I2CAddress, Reg_GO_COMMAND, cmd);
}

// 硬复位
void drv_husb238_hardreset()
{
    uint8_t cmd = Hard_Reset;
    drv_husb238_writereg(HUSB238_I2CAddress, Reg_GO_COMMAND, cmd);
}

// 初始化
void drv_husb238_init(int sda, int scl)
{
    i2c_port_t i2c_master_port = I2C_NUM;
    i2c_config_t conf;
    conf.mode = I2C_MODE_MASTER;
    conf.sda_io_num = sda;
    conf.sda_pullup_en = GPIO_PULLUP_DISABLE;
    conf.scl_io_num = scl;
    conf.scl_pullup_en = GPIO_PULLUP_DISABLE;
    conf.master.clk_speed = I2C_FREQ_HZ;
    conf.clk_flags = 0;
    ESP_ERROR_CHECK(i2c_param_config(i2c_master_port, &conf));
    ESP_ERROR_CHECK(i2c_driver_install(i2c_master_port, conf.mode, I2C_RX_BUF_DISABLE, I2C_TX_BUF_DISABLE, 0));
    initlized = true;
}

// 设置PD电压值
void drv_husb238_selvoltage(HUSB238_SELECT_Voltage_e pdo)
{
    HUSB238_Reg_SRC_PDO_SEL targetPDO;
    targetPDO.all = 0xA;
    targetPDO.bit.PDO_SELECT = pdo;
    drv_husb238_writereg(HUSB238_I2CAddress, Reg_SRC_PDO_SEL, targetPDO.all);
    drv_husb238_requestpdo();
}

// 读取PD当前状态寄存器值
void drv_husb238_getcurrentmode(HUSB238_Voltage_e *voltage, HUSB238_CURRENT_e *current)
{
    uint8_t reag[2] = {0};
    HUSB238_Reg_PD_STATUS0 status0;
    HUSB238_Reg_PD_STATUS1 status1;
    drv_husb238_i2creadbytes(HUSB238_I2CAddress, 0x00, 2, (uint8_t *)&reag);
    ((uint8_t *)&status0)[0] = reag[0];
    ((uint8_t *)&status1)[0] = reag[1];
    if(DEBUG_LOG){
        printf("status0.all = 0x%x\n", status0.all);
        printf("status1.all = 0x%x\n", status1.all);
    }

    *current = status0.bit.PD_SRC_CURRENT;
    *voltage = status0.bit.PD_SRC_VOLTAGE;
}

// 读取到的电压寄存器值转化电压值
uint16_t drv_husb238_voltage2pdo(HUSB238_Voltage_e voltage)
{
    if(DEBUG_LOG){
        printf("voltage: %d => %d\n", voltage, votlage_Table[voltage]);
    }
    return votlage_Table[voltage];
}

// 转化寄存器电流对应值
float to_current(HUSB238_CURRENT_e c)
{
    uint8_t i = c & 0xf;
    if (i <= PD_5A)
    {
        if(DEBUG_LOG){
            printf("current: %d => %.2f\n", c, current_Table[i]);
        }
        return current_Table[i];
    }
    else
    {
        if(DEBUG_LOG){
            printf("current: %d => %.2f\n", c, 0.0f);
        }
        return 0.0f;
    }
}
void drv_husb238_selcurrent(HUSB238_CURRENT_e current)
{
    drv_husb238_writereg(HUSB238_I2CAddress, Reg_SRC_PDO_SEL, (uint8_t)current);
    if(DEBUG_LOG)
    {
        printf("Current selected: %dA", current);
    }
   
}