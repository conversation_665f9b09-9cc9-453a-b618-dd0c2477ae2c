#include "type_channel_pool.h"
#include "logger.h"

#define tag "[Channel TABLE]"

int32_t channel_table_add(channel_element_t **pool, uint32_t value, uint32_t index)
{
    channel_element_t*  temp = (channel_element_t*)malloc(sizeof(channel_element_t));
    if (temp == NULL)
    {
        mlog_e(tag, "malloc error");
        return -1;
    }
    temp->element_id    = value;
    temp->channel_id    = index;
    HASH_ADD_INT(*pool, element_id, temp);
    return 0;
}

int32_t channel_table_check(channel_element_t **pool, uint32_t value)
{
    channel_element_t*  temp = NULL;
    int32_t find_i = value;
    HASH_FIND_INT(*pool, &find_i, temp);
    if (temp == NULL)
    {
        return -1;
    }
    return  temp->channel_id;
}


