#include "logger.h"
#include "local_device.h"
#include "pixel_common.h"

const pixel_hsv_t g_colour_table[COLOUR_MAX] =
{
	[WHITE]  = {0.0f,          0.0f, 0.0f},
    [RED]    = {0.0f,          1.0f, 0.0f},
    [ORANGE] = {30.0f/360.0f,  1.0f, 0.0f},
    [YELLOW] = {60.0f/360.0f,  1.0f, 0.0f},
    [GREEN]  = {120.0f/360.0f, 1.0f, 0.0f},
    [CYAN]   = {180.0f/360.0f, 1.0f, 0.0f},
    [BLUE]   = {240.0f/360.0f, 1.0f, 0.0f},
    [PURPLE] = {300.0f/360.0f, 1.0f, 0.0f},
    [PINK]   = {330.0f/360.0f, 1.0f, 0.0f},
};

