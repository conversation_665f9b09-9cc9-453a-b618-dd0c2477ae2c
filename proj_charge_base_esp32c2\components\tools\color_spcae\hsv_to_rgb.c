#include "hsv_to_rgb.h"
#include "math.h"
/*
H : 0-1
S : 0-1
V : 0-1
*/
void hsv_to_rgb(float H, float S, float V, uint8_t *r, uint8_t *g, uint8_t *b)
{
	if (S > 1.0f) S = 1.0f;
	if (V > 1.0f) V = 1.0f;
	
	float R = 0, G = 0, B = 0;
	float h = H;
	float s = S;
	float v = V;
	
	int i = floor(h * 6);
	float f = h * 6 - i;
	float p = v * (1 - s);
	float q = v * (1 - f * s);
	float t = v * (1 - (1 - f) * s);
	
	switch (i % 6) 
	{
		case 0: R = v, G = t, B = p; break;
		case 1: R = q, G = v, B = p; break;
		case 2: R = p, G = v, B = t; break;
		case 3: R = p, G = q, B = v; break;
		case 4: R = t, G = p, B = v; break;
		case 5: R = v, G = p, B = q; break;
	}
 
	*r = R * 255;
	*g = G * 255;
	*b = B * 255;
}

 

