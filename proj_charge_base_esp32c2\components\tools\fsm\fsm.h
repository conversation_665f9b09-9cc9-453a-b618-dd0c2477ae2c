#ifndef _FSM_H_
#define _FSM_H_

#include <stdbool.h>
#include <stdint.h>
#include <string.h>

#define	MAX_STA_NUM	(16)

typedef void (*p_process)(void);

typedef struct
{
	int8_t 		state_curr;
	int8_t 		state_next;
	int8_t 		state_last;
	
	p_process	prev_process[MAX_STA_NUM];
	p_process	curr_process[MAX_STA_NUM];
	p_process	post_process[MAX_STA_NUM];
	
}fsm_t;
  
#ifdef __cplusplus
 extern "C" {
#endif

	void fsm_init(fsm_t * fsm);

	void fsm_regist_state(fsm_t * fsm, 
					  uint8_t sta_enum,	// state enum, 0-max state
					  p_process prev,	// function, or NULL
					  p_process curr,
					  p_process post);

	void fsm_trans_to(fsm_t * fsm, uint8_t state);
	
	int8_t fsm_get_curr_state(fsm_t * fsm);
	
	int8_t fsm_get_next_state(fsm_t * fsm);
	
	int8_t fsm_get_last_state(fsm_t * fsm);

	void fsm_update(fsm_t * fsm);

#ifdef __cplusplus
 }
#endif

#endif
