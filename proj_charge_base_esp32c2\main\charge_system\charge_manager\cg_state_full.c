#include "logger.h"
#include "local_device.h"
#include "charge_manager.h"
#include "drv_ina226.h"
#include "drv_husb238.h"
#include "pixeleff_system.h"
#include "pixel_common.h"

#define tag  "[CG-State-FULL]"

static uint16_t voltage[6];
static float current[6];
void state_cg_full_prev_process()
{
 	mlog_i(tag, "Enter FULL state");
	pixeleff_system_colour(GREEN, 0.5);
	drv_husb238_getcapabilities(voltage, current);
}

void state_cg_full_curr_process()
{
	float power = ina226_get_power();

	// 如果功率小于100mW，说明设备已拔出，回到空闲状态
	if (power < 100)
	{
		mlog_i(tag, "Device disconnected, power: %.2f mW", power);
		drv_husb238_selvoltage(PDO_5V);
		charge_manager_transto(STA_CG_IDLE);
	}
}

void state_cg_full_post_process()
{
	mlog_i(tag, "Exit FULL state");
}