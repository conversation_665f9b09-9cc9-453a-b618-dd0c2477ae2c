#ifndef _H_TYPE_COMPORT_H
#define _H_TYPE_COMPORT_H

#include <stdint.h>
 
typedef struct
{
    int32_t (*init)(void);
    int32_t (*read)(uint8_t*, uint32_t);
    int32_t (*write)(uint8_t*, uint32_t);
}dev_comport_t;

typedef struct
{
    int32_t (*init)(void);
    int32_t (*rxtx)(uint8_t*, uint8_t*, uint32_t);    
}dev_spislave_t;

typedef struct
{
    int32_t (*init)(void);
    int32_t (*txrx)(uint8_t, uint8_t*, uint8_t*, uint32_t);    
    int32_t (*txrx_polling)(uint8_t, uint8_t*, uint8_t*, uint32_t);  
}dev_spimaster_t;

typedef struct
{
    int32_t (*init)(void);
}dev_i2c_bus_t;

#endif
