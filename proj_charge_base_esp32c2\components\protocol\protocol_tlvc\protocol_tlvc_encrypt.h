#ifndef _PROTOCOL_TLVC_ENCRYPT_H_
#define _PROTOCOL_TLVC_ENCRYPT_H_

#include <stdint.h>


#define ENCRYPT_MAX_IV_LENGTH    16
#define ENCRYPT_MAX_KEY_LENGTH   16


typedef void (*pfunc_tlv_callback)(uint8_t*, uint32_t);
typedef int32_t (*pfunc_generate_iv)(uint8_t *, uint8_t *);

typedef struct {
	// encrypt parameters
	uint8_t iv_length;
    uint8_t iv[ENCRYPT_MAX_IV_LENGTH];
	uint8_t decrypt_iv_len;
	uint8_t decrypt_iv_index;
    uint8_t decrypt_iv[ENCRYPT_MAX_IV_LENGTH];
	uint8_t key_length;
    uint8_t key[ENCRYPT_MAX_KEY_LENGTH];
	pfunc_generate_iv generate_iv;
} encrypt_para_t;

typedef struct
{
	// TAG
	char 		*tagstr;
	uint8_t  	taglen;	
	pfunc_tlv_callback pfunc;
} element_str_tlvc_t;

typedef struct
{
	// TAG
	uint8_t 	tagenum;
	pfunc_tlv_callback pfunc;
} element_enum_tlvc_t;

typedef struct
{
	uint8_t  		*data;
	uint32_t  		bytes;
	uint32_t  		length;
} data_buffer_t;

typedef struct
{
	uint32_t 		state;
	uint8_t 		head0;
	uint8_t 		head1;
	uint8_t 		len_h;
	uint8_t 		len_l;
	uint8_t			index;

	uint8_t 		crc8;
	uint16_t 		crc16;

	uint16_t  		total_len;
	uint16_t  		value_len;
		
	data_buffer_t   buffer;	

	uint32_t 		elem_num;
	element_str_tlvc_t  *elems;	

	uint8_t         cmdnum;
	element_enum_tlvc_t *cmdmap;

	uint8_t 		maptbl[0XFF];

	uint8_t         enc_enable;
	encrypt_para_t  encrypt;
} protocol_tlvc_enc_t;


#ifdef __cplusplus
 extern "C" {
#endif

int32_t protocol_tlvc_encrypt_string_regist(protocol_tlvc_enc_t *tlvc, 
									const char head0,
									const char head1,
									element_str_tlvc_t elems[], 
									uint32_t elemsize, 
									uint8_t* buf, 
									uint32_t buflen);
int32_t protocol_tlvc_encrypt_string_regist_malloc(protocol_tlvc_enc_t *tlvc, 
									const char head0,
									const char head1,
									element_str_tlvc_t elems[], 
									uint32_t elemsize, 
									uint8_t* buf, 
									uint32_t buflen);

int32_t protocol_tlvc_encrypt_cmdmap_regist(protocol_tlvc_enc_t *tlvc, 
									const char head0,
									const char head1,
									element_enum_tlvc_t cmdmap[], 
									uint32_t cmdnum, 
									uint8_t* buf, 
									uint32_t buflen);

int32_t protocol_tlvc_encrypt_cmdmap_regist_malloc(protocol_tlvc_enc_t *tlvc, 
									const char head0,
									const char head1,
									element_enum_tlvc_t cmdmap[], 
									uint32_t cmdnum, 
									uint8_t* buf, 
									uint32_t buflen);

int32_t protocol_tlvc_encrypt_init( protocol_tlvc_enc_t *tlvc,
									uint8_t enable_encrypt,
									const uint8_t *key, 
									uint8_t keylen,
									pfunc_generate_iv pfunc_iv);

int32_t protocol_tlvc_encrypt_decode(protocol_tlvc_enc_t *tlvc, uint8_t *data, uint32_t len);

int32_t protocol_tlvc_encrypt_pack(protocol_tlvc_enc_t *tlvc,
									const char head0,
									const char head1,
									const char *tag,
									uint32_t taglen,
									uint8_t *value,
									uint32_t valuelen,
									uint8_t *data,
									uint32_t datalen);

int32_t protocol_tlvc_encrypt_cmdmap_pack(protocol_tlvc_enc_t *tlvc,
									const char head0,
									const char head1,
									uint8_t cmdenum,
									uint32_t cmdlen,
									uint8_t *value,
									uint32_t valuelen,
									uint8_t *data,
									uint32_t datalen);

int32_t protocol_tlvc_encrypt_regist_value(protocol_tlvc_enc_t *tlvc, 
											const char head0,
											const char head1,
											uint8_t* buf, 
											uint32_t buflen);

int32_t protocol_tlvc_encrypt_decode_value(protocol_tlvc_enc_t *tlvc,
											uint8_t *din, 
											uint32_t lin, 
											uint8_t *dout, 
											uint32_t *lout);

int32_t protocol_tlvc_encrypt_reset(protocol_tlvc_enc_t *tlvc);

#ifdef __cplusplus
 }
#endif

#endif
