{"sources": [{"file": "D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/CMakeFiles/bootloader"}, {"file": "D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/CMakeFiles/bootloader.rule"}, {"file": "D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/CMakeFiles/bootloader-complete.rule"}, {"file": "D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}