#include "local_device.h"
#include "logger.h" 
#include "pixeleff_manager.h"
#include "pixeleff_system.h"

#define tag  "[ChargeManager]"

 
int32_t pixeleff_manager_init()
{
	pixeleff_system_init();
	return 0;
}

void pixeleff_manager_update()   
{
	pixeleff_system_update();
	// output
	static uint32_t last_tx = 0;
	if (dev_charge()->time.get_millis() - last_tx > 2)
	{
		last_tx = dev_charge()->time.get_millis();
		//mlog_e(tag, "pixeleff_manager_update");
		dev_charge()->pixel_onboard.flush();
	}
}
