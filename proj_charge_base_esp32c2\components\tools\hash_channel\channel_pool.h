#ifndef _H_CHANNEL_POOL_H
#define _H_CHANNEL_POOL_H

#include "type_channel_pool.h"

#ifdef __cplusplus
extern "C" {
#endif

int32_t channel_pool_init(channel_element_t **pool, uint32_t max_channel);

int32_t channel_pool_check(channel_element_t **pool, int32_t elem_id);

int32_t channel_pool_in(channel_element_t **pool, int32_t elem_id);

int32_t channel_pool_out(channel_element_t **pool, int32_t elem_id);

int32_t channel_pool_all_out(channel_element_t **pool);

int32_t channel_pool_get_num(channel_element_t **pool);

int32_t channel_pool_dump(channel_element_t **pool, int32_t *online, int32_t *num);

#ifdef __cplusplus
}
#endif

#endif /* _H_CHANNEL_POOL_H */
