#include <stdint.h>
#include <string.h>
#include <stdio.h>
#include <ctype.h>


#if 0

void demo()
{
	char strin[128] = "aasas 1111 dsdsd ffff";    
    char *cmdout[10];    
    uint32_t outlen = string_spilit(strin, strlen(strin), cmdout, 10);
  
    for (uint32_t i = 0; i < outlen; i++)
    {
        mlog_w(tag, "[%s]", cmdout[i]);
        free(cmdout[i]);
    }
    
}
#endif

// !!!! don't forget free cmdout after use
int32_t string_spilit(char *str, uint32_t len, char **cmdout, uint32_t cmdmax)
{
    // clear
    uint32_t str_length = strlen((char*)str);
    if (str_length > len)
    {
        str[len] = ' ';
    } 

    uint32_t cmd_num = 0;
    char *str_curr = strtok((char *)str, " ");
    while ((str_curr != NULL) && (cmd_num < cmdmax))
    {
        uint32_t strlength  = strlen(str_curr) + 1;
        //printf("[%d][%s]", strlength, str_curr);
        cmdout[cmd_num] = (char*)malloc(strlength);
        if (cmdout[cmd_num])
        {        	
        	memcpy(cmdout[cmd_num], str_curr, strlength);
        }        

        cmd_num ++;
        str_curr = strtok(NULL, " ");
    }
    return cmd_num;
}

int32_t string_spilit_static(char *str, uint32_t len, char **cmdout, uint32_t cmdmax)
{
    // clear
    uint32_t str_length = strlen((char*)str);
    if (str_length > len)
    {
        str[len] = ' ';
        uint32_t clear_len = str_length - len;
        memset(&str[len], 0, clear_len);
    }

    uint32_t cmd_num = 0;
    char *str_curr = strtok((char *)str, " ");
    while ((str_curr != NULL) && (cmd_num < cmdmax))
    {
        uint32_t strlength  = strlen(str_curr) + 1;
        if (cmdout[cmd_num])
        {
            memcpy(cmdout[cmd_num], str_curr, strlength);
        }

        cmd_num ++;
        str_curr = strtok(NULL, " ");
    }
    return cmd_num;
}


int32_t version_string_to_numeric(const char *str_ver, uint32_t *value_ver) 
{
    uint32_t v = 0;
    int part = 0;
    int digit;
    char *endptr;

    while (*str_ver && !isdigit((uint8_t)*str_ver)) 
    {
        str_ver++;
    }

    while (*str_ver) 
    {
        digit = strtol(str_ver, &endptr, 10);
        if (endptr == str_ver) 
        {
            return -1;
        }
        v |= (uint32_t)digit << (24 - (part * 8));
        str_ver = endptr;

        if (*str_ver == '.') 
        {
            part++;
            if (part >= 4) 
            {
                break;
            }
            str_ver++;
        }
    }
    *value_ver = v;

    return 0;
}

int32_t version_numeric_to_string(uint32_t value_ver, char *str_ver) 
{
    char parts[5][4] = {0};
    int i;

    for (i = 3; i >= 0; i--) 
    {
        snprintf(parts[i], sizeof(parts[i]), "%d", (value_ver >> (i * 8)) & 0xFF);
    }

    // format "X.X.X.X"
    snprintf(str_ver, 18, "%s.%s.%s.%s", parts[3], parts[2], parts[1], parts[0]);

    return 0;
}

