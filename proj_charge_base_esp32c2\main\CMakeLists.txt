set(COMPONENT_ADD_INCLUDEDIRS
./
./charge_system
./charge_system/wifi_client
./charge_system/pixel_effect
./charge_system/pixel_effect/system
./charge_system/pixel_effect/common
./charge_system/charge_manager
# ./unit_test
)

set(COMPONENT_SRCS
./charge_base.c
./charge_system/charge_system.c
./charge_system/wifi_client/wifi_client.c
./charge_system/pixel_effect/pixeleff_manager.c
./charge_system/pixel_effect/system/pixeleff_system.c
./charge_system/pixel_effect/system/state_system.c
./charge_system/pixel_effect/common/pixel_common.c
./charge_system/pixel_effect/common/pixel_waves.c
./charge_system/charge_manager/charge_manager.c
./charge_system/charge_manager/cg_state_charging.c
./charge_system/charge_manager/cg_state_full.c
./charge_system/charge_manager/cg_state_idle.c
)


register_component()
