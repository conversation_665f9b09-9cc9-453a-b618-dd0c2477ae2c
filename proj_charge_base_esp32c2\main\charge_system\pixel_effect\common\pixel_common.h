#ifndef _H_PIXEL_COMMON_H_
#define _H_PIXEL_COMMON_H_

#include <stdint.h>
 
typedef enum 
{
    WHITE = 0,  
    RED,         
    ORANGE,     
    YELLOW,      
    GREEN,       
    CYAN,        
    BLUE,       
    PURPLE,     
    PINK,
    COLOUR_MAX    
} colour_t;


typedef struct
{
	float 	h;
	float 	s;
	float 	v;
}pixel_hsv_t;

typedef struct
{
	uint8_t r;
	uint8_t g;
	uint8_t b;
}pixel_rgb_t;

typedef struct
{
	pixel_hsv_t 	hsv;
	pixel_rgb_t 	rgb;
}pixels_system_t;

#ifdef __cplusplus
extern "C" {
#endif

extern const pixel_hsv_t g_colour_table[COLOUR_MAX];
#ifdef __cplusplus
}
#endif

#endif
