#ifndef _TCP_SERVER_H_
#define _TCP_SERVER_H_

#include <stdint.h>

typedef void (*tcpserver_recv_call)(uint8_t *, uint32_t );

#ifdef __cplusplus
 extern "C" {
#endif

int32_t tcp_server_start(void);
void tcp_server_regist_data_call(tcpserver_recv_call pcall);
int32_t tcp_server_send(uint8_t *data, uint32_t len);
int32_t tcp_server_suspend(void);
int32_t tcp_server_resume(void);

#ifdef __cplusplus
 }
#endif

#endif

