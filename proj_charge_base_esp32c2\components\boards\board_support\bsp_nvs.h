#ifndef _BSP_NVS_H
#define _BSP_NVS_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

int32_t bsp_nvs_init(void);
int32_t bsp_nvs_set_str(const char* key, const char* in_value);
int32_t bsp_nvs_get_str(const char* key, char* out_value);
int32_t bsp_nvs_set_u32(const char* key, uint32_t in_value);
int32_t bsp_nvs_get_u32(const char* key, uint32_t* out_value);
int32_t bsp_nvs_set_i8(const char* key, int8_t in_value);
int32_t bsp_nvs_get_i8(const char* key, int8_t* out_value);
int32_t bsp_nvs_set_u8(const char* key, uint8_t in_value);
int32_t bsp_nvs_get_u8(const char* key, uint8_t* out_value);
int32_t bsp_nvs_set_i16(const char* key, int16_t in_value);
int32_t bsp_nvs_get_i16(const char* key, int16_t* out_value);
int32_t bsp_nvs_set_u16(const char* key, uint16_t in_value);
int32_t bsp_nvs_get_u16(const char* key, uint16_t* out_value);
int32_t bsp_nvs_set_blob(const char* key, uint8_t* data , uint32_t len);
int32_t bsp_nvs_get_blob(const char* key, uint8_t* data, uint32_t *len);

#ifdef __cplusplus
}
#endif

#endif /* _BSP_NVS_H */
