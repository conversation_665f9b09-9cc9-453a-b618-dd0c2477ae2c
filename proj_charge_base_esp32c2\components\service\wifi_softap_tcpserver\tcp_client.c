/* TCP Client Example for ESP32

   This example code is based on ESP-IDF examples and is in the Public Domain (or CC0 licensed, at your option.)

   Unless required by applicable law or agreed to in writing, this
   software is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
   CONDITIONS OF ANY KIND, either express or implied.
*/
#include <string.h>
#include <sys/param.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_system.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_log.h"
#include "nvs_flash.h"
#include "esp_netif.h"

#include "lwip/err.h"
#include "lwip/sockets.h"
#include "lwip/sys.h"
#include <lwip/netdb.h>

#include "tcp_client.h"

#define SERVER_PORT             (9523)
#define KEEPALIVE_IDLE          (5)
#define KEEPALIVE_INTERVAL      (5)
#define KEEPALIVE_COUNT         (5)
#define RECV_BUFFER_SIZE        (2048 + 128)
#define WIFI_CONNECTED_BIT      (BIT0)
#define WIFI_FAIL_BIT           (BIT1)
#define TCP_CONNECTED_BIT       (BIT2)
#define TCP_FAIL_BIT            (BIT3)
#define MAX_RETRY               (5)

static const char *TAG = "tcp_client";
static esp_netif_t *s_sta_netif = NULL;
static EventGroupHandle_t s_wifi_event_group = NULL;
static EventGroupHandle_t s_tcp_event_group;
static int s_retry_num = 0;
static int s_sock = -1;
static tcpclient_recv_callback s_recv_callback = NULL;
static tcpclient_scan_callback s_scan_callback = NULL;
static TaskHandle_t s_tcp_task_handle = NULL;
static TaskHandle_t s_wifi_scan_task_handle = NULL;
static bool s_is_connected = false;
static char s_target_prefix[32] = {0};
static char s_password[64] = {0};

static StaticEventGroup_t s_wifi_event_group_buffer;
static esp_event_handler_instance_t s_instance_any_id = NULL;
static esp_event_handler_instance_t s_instance_got_ip = NULL;

static uint8_t target_ap_num = 0;
static wifi_account_t target_wifi_ap[MAX_AP_NUM];


int32_t tcp_client_is_connected(void);
int32_t tcp_client_send(uint8_t *data, uint32_t len);
int32_t tcp_client_stop(void);
void tcp_client_register_scan_callback(tcpclient_scan_callback callback);
static void tcp_client_task(void *pvParameters);
static void wifi_scan_connect_task(void *pvParameters);

// WiFi event handler
static void wifi_event_handler(void* arg, esp_event_base_t event_base,
                               int32_t event_id, void* event_data)
{
    if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_START) {
        ESP_LOGI(TAG, "WiFi STA started");
    } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_DISCONNECTED) {
        if (s_retry_num < MAX_RETRY) {
            esp_wifi_connect();
            s_retry_num++;
            ESP_LOGI(TAG, "Retry to connect to the AP");
        } else {
            xEventGroupSetBits(s_wifi_event_group, WIFI_FAIL_BIT);
            s_is_connected = false;
        }
        ESP_LOGI(TAG, "Connect to the AP failed");
    } else if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
        ESP_LOGI(TAG, "Got IP:" IPSTR, IP2STR(&event->ip_info.ip));
        s_retry_num = 0;
        xEventGroupSetBits(s_wifi_event_group, WIFI_CONNECTED_BIT);
    }
}

// Initialize WiFi as station
static esp_err_t wifi_init_sta(void)
{
    s_wifi_event_group = xEventGroupCreateStatic(&s_wifi_event_group_buffer);

    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    vTaskDelay(pdMS_TO_TICKS(10));

    s_sta_netif = esp_netif_create_default_wifi_sta();
    vTaskDelay(pdMS_TO_TICKS(10));

    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    cfg.static_rx_buf_num = 3;
    cfg.static_tx_buf_num = 3;
    cfg.dynamic_rx_buf_num = 8;
    cfg.dynamic_tx_buf_num = 8;
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));
    vTaskDelay(pdMS_TO_TICKS(10));

    ESP_ERROR_CHECK(esp_event_handler_instance_register(WIFI_EVENT,
                                                        ESP_EVENT_ANY_ID,
                                                        &wifi_event_handler,
                                                        NULL,
                                                        &s_instance_any_id));
    ESP_ERROR_CHECK(esp_event_handler_instance_register(IP_EVENT,
                                                        IP_EVENT_STA_GOT_IP,
                                                        &wifi_event_handler,
                                                        NULL,
                                                        &s_instance_got_ip));

    vTaskDelay(pdMS_TO_TICKS(10));
    ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_STA));
    ESP_ERROR_CHECK(esp_wifi_start());

    ESP_LOGI(TAG, "wifi_init_sta finished.");
    return ESP_OK;
}

// 清理函数
void wifi_deinit_sta(void)
{
    if (s_instance_got_ip != NULL) {
        esp_event_handler_instance_unregister(IP_EVENT, IP_EVENT_STA_GOT_IP, s_instance_got_ip);
        s_instance_got_ip = NULL;
    }
    
    if (s_instance_any_id != NULL) {
        esp_event_handler_instance_unregister(WIFI_EVENT, ESP_EVENT_ANY_ID, s_instance_any_id);
        s_instance_any_id = NULL;
    }
    
    esp_wifi_stop();
    esp_wifi_deinit();
    
    if (s_sta_netif != NULL) {
        esp_netif_destroy(s_sta_netif);
        s_sta_netif = NULL;
    }
    
    esp_netif_deinit();
    
    // 事件组是静态分配的，不需要删除，但需要重置
    if (s_wifi_event_group != NULL) {
        xEventGroupClearBits(s_wifi_event_group, 0xFFFFFFFF);
    }
}

// Try to connect to a specified AP
int32_t try_connect_to_ap(const char* ssid, const char* password)
{
    wifi_config_t wifi_config = {0};
    
    // Set WiFi SSID and password
    strlcpy((char*)wifi_config.sta.ssid, ssid, sizeof(wifi_config.sta.ssid));
    strlcpy((char*)wifi_config.sta.password, password, sizeof(wifi_config.sta.password));
    
    ESP_LOGI(TAG, "Trying to connect to %s...", ssid);
    
    // Clear WiFi connection event bits
    xEventGroupClearBits(s_wifi_event_group, WIFI_CONNECTED_BIT | WIFI_FAIL_BIT);
    
    // Set WiFi configuration and connect
    ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_STA, &wifi_config));
    ESP_ERROR_CHECK(esp_wifi_start());
    ESP_ERROR_CHECK(esp_wifi_connect());
    
    // Wait for connection or failure event
    EventBits_t bits = xEventGroupWaitBits(s_wifi_event_group,
                                           WIFI_CONNECTED_BIT | WIFI_FAIL_BIT,
                                           pdFALSE,
                                           pdFALSE,
                                           pdMS_TO_TICKS(10000));
    
    if (bits & WIFI_CONNECTED_BIT) {
        ESP_LOGI(TAG, "Connected to AP SSID: %s", ssid);
        s_is_connected = true;
        return true;
    } else if (bits & WIFI_FAIL_BIT) {
        ESP_LOGI(TAG, "Failed to connect to SSID: %s", ssid);
        return false;
    } else {
        ESP_LOGE(TAG, "Connection timeout");
        return false;
    }
}

// WiFi scan and connect task
static void wifi_scan_connect_task(void *pvParameters)
{
    wifi_scan_config_t scan_config = {
        .ssid = NULL,
        .bssid = NULL,
        .channel = 0,
        .show_hidden = true
    };
    ESP_LOGI(TAG, "wifi_scan_connect_task create success.\r\n");
    while (1) {
        if (s_is_connected) {
            // If already connected, delay before next scan
            vTaskDelay(pdMS_TO_TICKS(5000));
            continue;
        }
        
        // Start scanning for WiFi networks
        ESP_LOGI(TAG, "Start scanning for WiFi networks...");
        ESP_ERROR_CHECK(esp_wifi_scan_start(&scan_config, true));
        
        // Get number of APs found
        uint16_t ap_count = 0;
        ESP_ERROR_CHECK(esp_wifi_scan_get_ap_num(&ap_count));
        
        if (ap_count == 0) {
            ESP_LOGI(TAG, "No AP found");
            vTaskDelay(pdMS_TO_TICKS(3000));
            continue;
        }
        
        // Get AP records
        uint16_t num = MIN(ap_count, MAX_AP_NUM);
        wifi_ap_record_t ap_records[MAX_AP_NUM];
        ESP_ERROR_CHECK(esp_wifi_scan_get_ap_records(&num, ap_records));
#if 1   
        // Iterate through all found APs to find those matching the prefix
        for (int i = 0; i < num; i++) {
            // Check if SSID matches target prefix
            if (strncmp((char*)ap_records[i].ssid, s_target_prefix, strlen(s_target_prefix)) == 0) {
                strlcpy(target_wifi_ap[target_ap_num].ssid, (const char *)ap_records[i].ssid, 32);
                strlcpy(target_wifi_ap[target_ap_num].password, (const char *)s_password, 16);
                target_ap_num++; // Increment counter for matching APs
                ESP_LOGI(TAG, "Found LiberAP: %d - SSID: %s, RSSI: %d", target_ap_num, ap_records[i].ssid, ap_records[i].rssi);
            }
        }

        // Log the summary of APs found with the specified prefix
        if (target_ap_num > 0) {
            ESP_LOGI(TAG, "Scan complete. Found and processed %d AP(s) with prefix '%s'.", target_ap_num, s_target_prefix);
            if (s_scan_callback) {
                s_scan_callback(target_ap_num, (wifi_account_t *)&target_wifi_ap);
            }
            // callback 
            s_wifi_scan_task_handle = NULL;
            vTaskDelete(NULL);
        } else {
            ESP_LOGI(TAG, "Scan complete. No APs found with prefix '%s'.", s_target_prefix);
        }
#else       
        ESP_LOGI(TAG, "Found %d access points:", num);
        for (int i = 0; i < num; i++) {
            ESP_LOGI(TAG, "%d: SSID %s, RSSI %d", i + 1, ap_records[i].ssid, ap_records[i].rssi);
            
            // Check if SSID matches target prefix
            if (strncmp((char*)ap_records[i].ssid, s_target_prefix, strlen(s_target_prefix)) == 0) {
                ESP_LOGI(TAG, "Found matching AP: %s", ap_records[i].ssid);
                
                // Try to connect to AP
                if (try_connect_to_ap((char*)ap_records[i].ssid, s_password)) {
                    // Start TCP client task after successful connection
                    if (s_tcp_task_handle == NULL) {
                        xTaskCreate(tcp_client_task, "tcp_client", 4096, NULL, 5, &s_tcp_task_handle);
                    }
                    break;
                }
            }
        }
#endif        
        // Delay before next scan if not connected
        if (!s_is_connected) {
            vTaskDelay(pdMS_TO_TICKS(5000));
        }
    }
}

// TCP client task
static void tcp_client_task(void *pvParameters)
{
    s_tcp_event_group = xEventGroupCreate();
    char rx_buffer[RECV_BUFFER_SIZE];
    char host_ip[16] = "***********"; // Default SoftAP IP address
    int addr_family = AF_INET;
    int ip_protocol = IPPROTO_IP;
    struct sockaddr_in dest_addr;
    ESP_LOGI(TAG, "tcp_client_task create success.\r\n");
    while (1) {
        // Wait until WiFi is connected
        if (!s_is_connected) {
            vTaskDelay(pdMS_TO_TICKS(1000));
            continue;
        }
        
        // Prepare destination address structure
        dest_addr.sin_addr.s_addr = inet_addr(host_ip);
        dest_addr.sin_family = AF_INET;
        dest_addr.sin_port = htons(SERVER_PORT);
        
        // Create socket
        s_sock = socket(addr_family, SOCK_STREAM, ip_protocol);
        if (s_sock < 0) {
            ESP_LOGE(TAG, "Unable to create socket: errno %d", errno);
            vTaskDelay(pdMS_TO_TICKS(1000));
            continue;
        }
        
        // Set TCP keepalive options
        int keepAlive = 1;
        int keepIdle = KEEPALIVE_IDLE;
        int keepInterval = KEEPALIVE_INTERVAL;
        int keepCount = KEEPALIVE_COUNT;
        setsockopt(s_sock, SOL_SOCKET, SO_KEEPALIVE, &keepAlive, sizeof(int));
        setsockopt(s_sock, IPPROTO_TCP, TCP_KEEPIDLE, &keepIdle, sizeof(int));
        setsockopt(s_sock, IPPROTO_TCP, TCP_KEEPINTVL, &keepInterval, sizeof(int));
        setsockopt(s_sock, IPPROTO_TCP, TCP_KEEPCNT, &keepCount, sizeof(int));
        
        // Connect to server
        ESP_LOGI(TAG, "Connecting to server %s:%d...", host_ip, SERVER_PORT);
        int err = connect(s_sock, (struct sockaddr *)&dest_addr, sizeof(dest_addr));
        if (err != 0) {
            ESP_LOGE(TAG, "Socket unable to connect: errno %d", errno);
            close(s_sock);
            s_sock = -1;
            vTaskDelay(pdMS_TO_TICKS(1000));
            continue;
        }
        
        ESP_LOGI(TAG, "Successfully connected to server");
        xEventGroupSetBits(s_tcp_event_group, TCP_CONNECTED_BIT);
        
        // Receive data loop
        while (1) {
            int len = recv(s_sock, rx_buffer, sizeof(rx_buffer) - 1, 0);
            if (len < 0) {
                ESP_LOGE(TAG, "Error occurred during receiving: errno %d", errno);
                break;
            } else if (len == 0) {
                ESP_LOGW(TAG, "Connection closed by the server");
                break;
            } else {
                // Call registered receive callback with received data
                if (s_recv_callback) {
                    s_recv_callback((uint8_t*)rx_buffer, len);
                }
            }
        }
        
        // Close socket after disconnect
        if (s_sock != -1) {
            ESP_LOGI(TAG, "Closing socket");
            shutdown(s_sock, 0);
            close(s_sock);
            s_sock = -1;
        }
        
        xEventGroupSetBits(s_tcp_event_group, TCP_FAIL_BIT);
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
}

// Start TCP client: initialize NVS, WiFi, and start scan/connect task
int32_t tcp_client_start(const char* target_prefix, const char* password)
{
    // Initialize NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    
    // Copy target SSID prefix and password
    strlcpy(s_target_prefix, target_prefix, sizeof(s_target_prefix));
    strlcpy(s_password, password, sizeof(s_password));
    
    // Initialize WiFi as station
    wifi_init_sta();
    
    // Create WiFi scan and connect task
    xTaskCreate(wifi_scan_connect_task, "wifi_scan", 8192, NULL, 5, &s_wifi_scan_task_handle);

    xTaskCreate(tcp_client_task, "tcp_client", 8192, NULL, 5, &s_tcp_task_handle);
    
    return 0;
}

// Register data receive callback
void tcp_client_register_recv_callback(tcpclient_recv_callback callback)
{
    s_recv_callback = callback;
}

// Register scan finished callback
void tcp_client_register_scan_callback(tcpclient_scan_callback callback)
{
    s_scan_callback = callback;
}

// Send data to TCP server
int32_t tcp_client_send(uint8_t *data, uint32_t len)
{
    if (s_sock < 0 || !s_is_connected) {
        ESP_LOGE(TAG, "Not connected to server");
        return -1;
    }
    
    int err = send(s_sock, data, len, 0);
    if (err < 0) {
        ESP_LOGE(TAG, "Error occurred during sending: errno %d", errno);
        return -1;
    }
    
    return err;
}

// Stop TCP client: close socket, disconnect WiFi, delete tasks
int32_t tcp_client_stop(void)
{
    // Close socket if open
    if (s_sock != -1) {
        shutdown(s_sock, 0);
        close(s_sock);
        s_sock = -1;
    }
    
    // Disconnect and stop WiFi
    esp_wifi_disconnect();
    esp_wifi_stop();
    
    // Delete TCP client task
    // if (s_tcp_task_handle != NULL) {
    //     vTaskDelete(s_tcp_task_handle);
    //     s_tcp_task_handle = NULL;
    // }
    
    // // Delete WiFi scan task
    // if (s_wifi_scan_task_handle != NULL) {
    //     vTaskDelete(s_wifi_scan_task_handle);
    //     s_wifi_scan_task_handle = NULL;
    // }
    
    s_is_connected = false;
    
    return 0;
}

// disconnect wifi only
int32_t disconnect_wifi_only(void)
{
    ESP_LOGI(TAG, "Attempting to disconnect Wi-Fi only. Socket s_sock (%d) will remain open.", s_sock);

    esp_err_t err_disconnect = esp_wifi_disconnect();
    if (err_disconnect == ESP_OK) {
        ESP_LOGI(TAG, "esp_wifi_disconnect() successful.");
    } else if (err_disconnect == ESP_ERR_WIFI_NOT_STARTED) {
        ESP_LOGI(TAG, "Wi-Fi was not started, no need to disconnect (esp_wifi_disconnect).");
    } else if (err_disconnect == ESP_ERR_WIFI_NOT_CONNECT) {
        ESP_LOGI(TAG, "Wi-Fi was not connected, no actual disconnection needed (esp_wifi_disconnect).");
    } else {
        ESP_LOGE(TAG, "esp_wifi_disconnect() failed: %s", esp_err_to_name(err_disconnect));
        return -1; 
    }

    esp_err_t err_stop = esp_wifi_stop();
    if (err_stop == ESP_OK) {
        ESP_LOGI(TAG, "esp_wifi_stop() successful.");
    } else if (err_stop == ESP_ERR_WIFI_NOT_INIT) {
        ESP_LOGI(TAG, "Wi-Fi was not initialized, no need to stop (esp_wifi_stop).");
    } else {
        ESP_LOGE(TAG, "esp_wifi_stop() failed: %s", esp_err_to_name(err_stop));
        return -2;
    }

    ESP_LOGI(TAG, "s_is_connected set to false. Wi-Fi is now disconnected.");

    return 0;
}

// Get current connection status
int32_t tcp_client_is_connected(void)
{
    return s_is_connected ? 1 : 0;
}
