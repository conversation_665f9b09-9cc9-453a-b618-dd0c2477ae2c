#ifndef _TCP_CLIENT_H_
#define _TCP_CLIENT_H_

#include <stdint.h>

#define MAX_AP_NUM              (10)

typedef struct
{
    char ssid[32];             /**< SSID of target AP. */
    char password[16];         /**< Password of target AP. */
}wifi_account_t;

typedef void (*tcpclient_recv_callback)(uint8_t *, uint32_t);
typedef void (*tcpclient_scan_callback)(uint8_t, wifi_account_t *);

#ifdef __cplusplus
 extern "C" {
#endif

/**
 * @brief Start the TCP client, including WiFi scanning, connection, and TCP communication
 * 
 * @param target_prefix Target WiFi SSID prefix used to filter scan results
 * @param password WiFi password to attempt connection
 * @return int32_t 0 indicates success, negative value indicates failure
 */
int32_t tcp_client_start(const char* target_prefix, const char* password);

/**
 * @brief Register a data receive callback function
 * 
 * @param callback Callback function called when data is received
 */
void tcp_client_register_recv_callback(tcpclient_recv_callback callback);

/**
 * @brief Register a scan result function
 * 
 * @param callback Callback function called when scan finished
 */
void tcp_client_register_scan_callback(tcpclient_scan_callback callback);

// Try to connect to a specified AP
int32_t try_connect_to_ap(const char* ssid, const char* password);

/**
 * @brief Send data to the TCP server
 * 
 * @param data Data to be sent
 * @param len Length of the data
 * @return int32_t Number of bytes sent, negative value indicates failure
 */
int32_t tcp_client_send(uint8_t *data, uint32_t len);

/**
 * @brief Stop the TCP client
 * 
 * @return int32_t 0 indicates success, negative value indicates failure
 */
int32_t tcp_client_stop(void);

/**
 * @brief Disconnect wifi only
 * 
 * @return int32_t 0 indicates success, negative value indicates failure
 */
int32_t disconnect_wifi_only(void);

/**
 * @brief Get the current connection status
 * 
 * @return int32_t 1 indicates connected, 0 indicates not connected
 */
int32_t tcp_client_is_connected(void);

#ifdef __cplusplus
 }
#endif

#endif /* _TCP_CLIENT_H_ */
