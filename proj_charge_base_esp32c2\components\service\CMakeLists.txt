# Edit following two lines to set component requirements (see docs)

set(COMPONENT_REQUIRES
    esp-tls 
	esp_http_client 
	app_update 
	esp_https_ota 
	bt 
	nvs_flash 
	tools 
	json
)
if (NOT(IDF_TARGET STREQUAL "esp32c2"))
	list(APPEND COMPONENT_REQUIRES
		esp_tinyusb
	)
else()
#
endif()

list(APPEND COMPONENT_ADD_INCLUDEDIRS 	./wifi_softap_tcpserver
										./gatt_server_table_app										
										./firmware_ota
										)
list(APPEND COMPONENT_SRCS
./firmware_ota/firmware_ota.c
)
if (CONFIG_ESP_WIFI_ENABLED)
list(APPEND COMPONENT_SRCS
./wifi_softap_tcpserver/tcp_client.c
)
endif()
if(CONFIG_BT_ENABLED)
list(APPEND COMPONENT_SRCS
./gatt_server_table_app/gatt_server_table_a01.c
# ./gatt_server_table_app/gatt_server_multi_connect.c
)
endif()

register_component()

