#include "logger.h"
#include "local_device.h"
#include "charge_manager.h"
#include "drv_ina226.h"
#include "drv_husb238.h"
#include "pixeleff_system.h"
#include "pixel_common.h"

#define tag  "[CG-State-charging]"

// 静态变量，用于跟踪充电阶段
static uint8_t charge_phase = 0; // 0: 未开始, 1: 已达到高功率, 2: 充电完成
void state_cg_charging_prev_process()
{
	mlog_i(tag, "Enter CHARGING state");
	pixeleff_system_breathe(2.0, WHITE);
	charge_phase = 0;
}

void state_cg_charging_curr_process()
{
	float power = ina226_get_power();

	// 如果功率小于100mW，说明设备已拔出，回到空闲状态
	if (power < 100)
	{
		mlog_i(tag, "Device disconnected, power: %.2f mW", power);
		charge_phase = 0; // 重置充电阶段
		charge_manager_transto(STA_CG_IDLE);
		return;
	}

	// 充电阶段状态机
	switch (charge_phase)
	{
		case 0: // 未开始充电阶段
			if (power >= 10000)
			{
				charge_phase = 1; // 进入高功率充电阶段
				mlog_i(tag, "Enter high power charging phase, power: %.2f mW", power);
			}
			break;

		case 1: // 高功率充电阶段
			if (power <= 2800)
			{
				charge_phase = 2; // 充电完成
				mlog_i(tag, "Charging completed, power: %.2f mW", power);
				charge_manager_transto(STA_CG_FULL);
			}
			break;

		case 2: // 充电完成阶段
			charge_manager_transto(STA_CG_FULL);
			break;

		default:
			charge_phase = 0;
			break;
	}
}

void state_cg_charging_post_process()
{
	mlog_i(tag, "Exit CHARGING state");
}