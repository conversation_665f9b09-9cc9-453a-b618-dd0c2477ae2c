#include "tmp_to_rgb.h"
#include "math.h"

void color_temp_to_rgb(float color_temp, uint8_t *rgb)
{
	float x = color_temp / 1000.0f;
	float x2 = x * x;
	float x3 = x2 * x;
	float x4 = x3 * x;
	float x5 = x4 * x;

	float R = 0.0f, G = 0.0f, B = 0.0f;

	// red
	if ( color_temp <= 6600.0f ) 
	{
		R = 1.0f;
	} else 
	{
		R = 0.0002889f * x5 - 0.01258f * x4 + 0.2148f * x3 - 1.776f * x2 + 6.907f * x - 8.723f;
	}

	// green
	if ( color_temp <= 6600.0f ) 
	{
		G = -4.593e-05f * x5 + 0.001424f * x4 - 0.01489f * x3 + 0.0498f * x2 + 0.1669f * x - 0.1653f;
	} else {
		G = -1.308e-07f * x5 + 1.745e-05f * x4 - 0.0009116f * x3 + 0.02348f * x2 - 0.3048f * x + 2.159f;
	}

	// blue
	if ( color_temp <= 2000.0f ) 
	{
		B = 0.0f;
	} else if ( color_temp < 6600.0f ) {
		B = 1.764e-05f * x5 + 0.0003575f * x4 - 0.01554f * x3 + 0.1549f * x2 - 0.3682f * x + 0.2386f;
	} else {
		B = 1.0f;
	}
 
	R = pow(R, 5.0f);
	G = pow(G, 5.0f);
	B = pow(B, 5.0f);
	
	rgb[0] = R * 255;
	rgb[1] = G * 255;
	rgb[2] = B * 255;
}
 

