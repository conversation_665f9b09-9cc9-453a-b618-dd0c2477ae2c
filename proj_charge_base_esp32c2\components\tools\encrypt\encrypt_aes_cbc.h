#ifndef __ENCRYPT_AES_CBC_H__
#define __ENCRYPT_AES_CBC_H__

#include "stdint.h"
#include "string.h"
#include "esp_timer.h"
#include "mbedtls/aes.h"
#include "encrypt_aes_cbc.h"

#define ENCRYPT_AES_CBC_BIT  128

#define MAX_LENGTH_OF_IV     16
#define MAX_LENGTH_OF_KEY    (ENCRYPT_AES_CBC_BIT / 8)


typedef struct {
    uint8_t aes_cbc_iv[MAX_LENGTH_OF_IV];
    uint8_t aes_cbc_key[MAX_LENGTH_OF_KEY];
    mbedtls_aes_context aes_ctx;
}encrypt_aes_cbc_t;


int32_t aes_cbc_set_iv(encrypt_aes_cbc_t *encrypt, uint8_t *iv, uint8_t iv_len);
int32_t aes_cbc_set_key(encrypt_aes_cbc_t *encrypt, uint8_t *key, uint8_t key_len);
int32_t aes_cbc_init(encrypt_aes_cbc_t *encrypt, uint8_t *iv, uint8_t iv_len, uint8_t *key, uint8_t key_len);
int32_t aes_cbc_deinit(encrypt_aes_cbc_t *encrypt);
int32_t encrypt_aes_cbc(encrypt_aes_cbc_t *encrypt, uint8_t *plain, int32_t plain_len, uint8_t *cipper);
int32_t decrypt_aes_cbc(encrypt_aes_cbc_t *encrypt, uint8_t *cipper, int32_t cipher_len, uint8_t *plain);


#endif

