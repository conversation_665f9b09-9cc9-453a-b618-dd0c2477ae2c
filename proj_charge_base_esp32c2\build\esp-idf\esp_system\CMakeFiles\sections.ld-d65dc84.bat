@echo off
cd /D D:\idf\ebed_framework_esp32\proj_charge_base_esp32c2\build\esp-idf\esp_system || (set FAIL_LINE=2& goto :ABORT)
d:\idf\python_env\idf5.4_py3.11_env\Scripts\python.exe D:/idf/Espressif/frameworks/esp-idf/tools/ldgen/ldgen.py --config D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/sdkconfig --fragments-list D:/idf/Espressif/frameworks/esp-idf/components/riscv/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_driver_gpio/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_pm/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_mm/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/spi_flash/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_system/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_system/app.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_common/common.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_common/soc.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_rom/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/hal/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/log/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/heap/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/soc/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_hw_support/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_hw_support/dma/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_hw_support/ldo/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/freertos/linker_common.lf;D:/idf/Espressif/frameworks/esp-idf/components/freertos/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/newlib/newlib.lf;D:/idf/Espressif/frameworks/esp-idf/components/newlib/system_libs.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_driver_gptimer/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_ringbuf/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_driver_uart/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/app_trace/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_event/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_driver_pcnt/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_driver_spi/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_driver_mcpwm/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_driver_ana_cmpr/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_driver_dac/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_driver_rmt/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_driver_sdm/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_driver_i2c/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_driver_ledc/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_driver_parlio/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_driver_usb_serial_jtag/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_phy/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/vfs/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/lwip/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_netif/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/wpa_supplicant/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_wifi/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_adc/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_driver_isp/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_eth/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_gdbstub/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_psram/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/esp_lcd/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/espcoredump/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/ieee802154/linker.lf;D:/idf/Espressif/frameworks/esp-idf/components/openthread/linker.lf --input D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/esp-idf/esp_system/ld/sections.ld.in --output D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/esp-idf/esp_system/ld/sections.ld --kconfig D:/idf/Espressif/frameworks/esp-idf/Kconfig --env-file D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/config.env --libraries-file D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/ldgen_libraries --objdump D:/idf/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/riscv32-esp-elf-objdump.exe || (set FAIL_LINE=3& goto :ABORT)
goto :EOF

:ABORT
set ERROR_CODE=%ERRORLEVEL%
echo Batch file failed at line %FAIL_LINE% with errorcode %ERRORLEVEL%
exit /b %ERROR_CODE%