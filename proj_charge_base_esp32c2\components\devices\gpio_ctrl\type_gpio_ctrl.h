#ifndef _H_TYPE_GPIO_CTRL_H
#define _H_TYPE_GPIO_CTRL_H

#include <stdint.h>

typedef struct
{
    int32_t (*init)(void);
    int32_t (*set_on)(void);        // set enable
    int32_t (*set_off)(void);       // set disable
}dev_gpio_ctrl_t;

typedef struct
{
    int32_t (*init)(void);
    int32_t (*set_on)(void);        // set enable
    int32_t (*set_off)(void);       // set disable
    int32_t (*set_mode)(void);        
}dev_gpio_analogh_sw_t;

#endif
