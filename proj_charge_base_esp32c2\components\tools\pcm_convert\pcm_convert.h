#ifndef __PCM_CONVERT_H__
#define __PCM_CONVERT_H__

#include <stdint.h>


void mono_float_to_pcm16(float* in, int16_t* out, size_t len);
void mono_pcm16_to_float(uint8_t* pcm, float* out, size_t len_bytes);
void stereo_pcm16_to_float( uint8_t* pcm,
                            float* left,
                            float* right,
                            size_t len_bytes);
void stereo_pcm16_spilit( uint8_t* pcm,
                          int16_t* left,
                          int16_t* right,
                          size_t len_bytes);
void stereo_pcm16_merge( int16_t* left, 
                         int16_t* right, 
                         size_t samples, 
                         uint8_t* stereo_out);

#endif

