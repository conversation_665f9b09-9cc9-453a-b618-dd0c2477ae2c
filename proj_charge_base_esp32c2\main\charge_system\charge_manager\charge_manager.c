#include "local_device.h"
#include "i2c_device.h"
#include "logger.h" 
#include "fsm.h"
#include "charge_manager.h"
#include "cg_state_idle.h"

#include "cg_state_charging.h"
#include "cg_state_full.h"

 
#define tag  "[ChargeManager]"

static fsm_t	fsm_charge;
 
int32_t charge_manager_init()
{
	fsm_init(&fsm_charge);
	fsm_regist_state(&fsm_charge, STA_CG_IDLE,
					 state_cg_idle_prev_process,
					 state_cg_idle_curr_process, 
					 state_cg_idle_post_process);

	fsm_regist_state(&fsm_charge, STA_CG_CHARGING,
					 state_cg_charging_prev_process,
					 state_cg_charging_curr_process, 
					 state_cg_charging_post_process);  

	fsm_regist_state(&fsm_charge, STA_CG_FULL,
					 state_cg_full_prev_process,
					 state_cg_full_curr_process, 
					 state_cg_full_post_process);  


	fsm_trans_to(&fsm_charge, STA_CG_IDLE);
	
	return 0;
}

void charge_manager_update()
{
	fsm_update(&fsm_charge);	
}

void charge_manager_transto(uint32_t state) 
{
	fsm_trans_to(&fsm_charge, state);	
}


