#include "drv_ina226.h"
#include "esp_log.h"
#include "driver/i2c.h"

// 定义调试日志标签
static const char *TAG = "INA226";
ina226_data_t ina226_data;
// 初始化 I2C 主机
void i2c_master_init(void) {
    // i2c_config_t conf = {
    //     .mode = I2C_MODE_MASTER,
    //     .sda_io_num = I2C_MASTER_SDA_IO,  // 设置 SDA 引脚
    //     .scl_io_num = I2C_MASTER_SCL_IO,  // 设置 SCL 引脚
    //     .master.clk_speed = I2C_MASTER_FREQ_HZ,  // 设置时钟频率
    // };
    // ESP_ERROR_CHECK(i2c_param_config(I2C_MASTER_NUM, &conf));
    // ESP_ERROR_CHECK(i2c_driver_install(I2C_MASTER_NUM, conf.mode, 0, 0, 0));
}

// 写入寄存器
esp_err_t ina226_write_register(uint8_t reg, uint16_t data) {
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (INA226_ADDR1 << 1) | I2C_MASTER_WRITE, true); // 发送设备地址（写操作）
    i2c_master_write_byte(cmd, reg, true);                                   // 发送寄存器地址
    i2c_master_write_byte(cmd, (data >> 8) & 0xFF, true);                    // 高字节
    i2c_master_write_byte(cmd, data & 0xFF, true);                           // 低字节
    i2c_master_stop(cmd);
    esp_err_t ret = i2c_master_cmd_begin(I2C_MASTER_NUM, cmd, pdMS_TO_TICKS(I2C_MASTER_TIMEOUT_MS));
    i2c_cmd_link_delete(cmd);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to write register 0x%02X", reg);
    }
    return ret;
}

// 读取寄存器
esp_err_t ina226_read_register(uint8_t reg, uint16_t *data) {
    uint8_t msb = 0, lsb = 0;
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (INA226_ADDR1 << 1) | I2C_MASTER_WRITE, true); // 发送设备地址（写操作）
    i2c_master_write_byte(cmd, reg, true);                                   // 发送寄存器地址
    i2c_master_start(cmd);                                                  // 重新启动
    i2c_master_write_byte(cmd, (INA226_ADDR1 << 1) | I2C_MASTER_READ, true); // 发送设备地址（读操作）
    i2c_master_read_byte(cmd, &msb, I2C_MASTER_ACK);                        // 读取高字节
    i2c_master_read_byte(cmd, &lsb, I2C_MASTER_NACK);                       // 读取低字节
    i2c_master_stop(cmd);
    esp_err_t ret = i2c_master_cmd_begin(I2C_MASTER_NUM, cmd, pdMS_TO_TICKS(I2C_MASTER_TIMEOUT_MS));
    i2c_cmd_link_delete(cmd);

    if (ret == ESP_OK) {
        *data = (msb << 8) | lsb; // 合并高低字节
    } else {
        ESP_LOGE(TAG, "Failed to read register 0x%02X", reg);
    }
    return ret;
}
// 初始化 INA226
void ina226_init(void) 
{
    // i2c_master_init();                                    // 初始化 I2C 主机
    ina226_write_register(CFG_REG, 0x8000);              // 重置 INA226
    ina226_write_register(CFG_REG, MODE_INA226);         // 配置 INA226 工作模式
    ina226_write_register(CAL_REG, CAL);                // 配置校准寄存器
}

void ina226_update()
{
    uint16_t raw_data;
    if (ina226_read_register(PWR_REG, &raw_data) == ESP_OK) 
    {
        ina226_data.power = raw_data * POWER_LSB;// 转换为实际功率值（单位 mW）
        ESP_LOGI(TAG, "Power: %.2f mW", ina226_data.power);
    }
    if (ina226_read_register(CUR_REG, &raw_data) == ESP_OK) 
    {
        ina226_data.current = raw_data* CURRENT_LSB;// 转换为实际电流值（单位 mA）  
        ESP_LOGI(TAG, "Current: %.2f mA", ina226_data.current);            
    }
    if (ina226_read_register(SV_REG, &raw_data) == ESP_OK) 
    {
        ina226_data.shunt_voltage = raw_data * INA226_VAL_LSB;// 转换为实际分流电压值（单位 uV）
        ESP_LOGI(TAG, "Shunt Voltage: %.2f uV", ina226_data.shunt_voltage);    
    }
    if (ina226_read_register(BV_REG, &raw_data) == ESP_OK) 
    {        
        ina226_data.bus_voltage = raw_data * Voltage_LSB; // 转换为实际电压值（单位 mV）
        ESP_LOGI(TAG, "Bus Voltage: %.2f mV", ina226_data.bus_voltage);
    }
}

// 获取总线电压
float ina226_get_voltage(void) 
{
    return ina226_data.bus_voltage;                                         
}

// 获取分流电压
float ina226_get_shunt_voltage(void) 
{
    return ina226_data.shunt_voltage;                                       
}

// 获取电流
float ina226_get_current(void) 
{
    return ina226_data.current;                                        
}

// 获取功率
float ina226_get_power(void) 
{
    return ina226_data.power;                                        
}

