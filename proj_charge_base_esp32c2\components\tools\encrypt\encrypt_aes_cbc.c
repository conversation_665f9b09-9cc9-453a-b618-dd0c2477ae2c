#include "encrypt_aes_cbc.h"


int32_t aes_cbc_set_iv(encrypt_aes_cbc_t *encrypt, uint8_t *iv, uint8_t iv_len)
{
    if (encrypt == NULL || iv == NULL || iv_len > MAX_LENGTH_OF_IV)
    {
        return -1;
    }
    memcpy(encrypt->aes_cbc_iv, iv, iv_len);

    return 0;
}

int32_t aes_cbc_set_key(encrypt_aes_cbc_t *encrypt, uint8_t *key, uint8_t key_len)
{
    if (encrypt == NULL || key == NULL || key_len > MAX_LENGTH_OF_KEY)
    {
        return -1;
    }
    memcpy(encrypt->aes_cbc_key, key, key_len);

    return 0;
}

int32_t aes_cbc_init(encrypt_aes_cbc_t *encrypt, uint8_t *iv, uint8_t iv_len, uint8_t *key, uint8_t key_len)
{
    if (encrypt == NULL || iv == NULL || iv_len > MAX_LENGTH_OF_IV || key == NULL || key_len > MAX_LENGTH_OF_KEY)
    {
        return -1;
    }
    memcpy(encrypt->aes_cbc_iv,  iv, iv_len);
    memcpy(encrypt->aes_cbc_key, key, key_len);
    mbedtls_aes_init(&encrypt->aes_ctx);

    return 0;
}

int32_t aes_cbc_deinit(encrypt_aes_cbc_t *encrypt)
{
    if (encrypt)
    {
        mbedtls_aes_free(&encrypt->aes_ctx);
    }
    return 0;
}

int32_t encrypt_aes_cbc(encrypt_aes_cbc_t *encrypt, uint8_t *plain, int32_t plain_len, uint8_t *cipper)
{
    int32_t ret = 0;
    uint8_t crypt_iv[MAX_LENGTH_OF_IV];
    
    if (plain == NULL || cipper == NULL || ((plain_len % 16) != 0))
    {
        return -1;
    }

    memcpy(crypt_iv, encrypt->aes_cbc_iv, MAX_LENGTH_OF_IV);
    ret |= mbedtls_aes_setkey_enc(&encrypt->aes_ctx, encrypt->aes_cbc_key, ENCRYPT_AES_CBC_BIT);
    ret |= mbedtls_aes_crypt_cbc(&encrypt->aes_ctx, MBEDTLS_AES_ENCRYPT, plain_len, crypt_iv, plain, cipper);
    return ret;
}

int32_t decrypt_aes_cbc(encrypt_aes_cbc_t *encrypt, uint8_t *cipper, int32_t cipher_len, uint8_t *plain)
{
    int32_t ret = 0;
    uint8_t crypt_iv[MAX_LENGTH_OF_IV];

    if (plain == NULL || cipper == NULL || ((cipher_len % 16) != 0))
    {
        return -1;
    }

    memcpy(crypt_iv, encrypt->aes_cbc_iv, MAX_LENGTH_OF_IV);
    ret |= mbedtls_aes_setkey_dec(&encrypt->aes_ctx, encrypt->aes_cbc_key, ENCRYPT_AES_CBC_BIT);
    ret |= mbedtls_aes_crypt_cbc(&encrypt->aes_ctx, MBEDTLS_AES_DECRYPT, cipher_len, crypt_iv, cipper, plain);
    return ret;
}

