#ifndef _H_LOGGER_H_
#define _H_LOGGER_H_

#include "esp_log.h"
#include "esp_timer.h"

#define mlog_e(tag, format, ... )   ESP_LOGE(tag, format, ##__VA_ARGS__)
#define mlog_w(tag, format, ... )   ESP_LOGW(tag, format, ##__VA_ARGS__)
#define mlog_i(tag, format, ... )   ESP_LOGI(tag, format, ##__VA_ARGS__)
#define mlog_d(tag, format, ... )   ESP_LOGD(tag, format, ##__VA_ARGS__)
#define mlog_v(tag, format, ... )   ESP_LOGV(tag, format, ##__VA_ARGS__)

#define mlog_early_e(tag, format, ... )   ESP_EARLY_LOGE(tag, format, ##__VA_ARGS__)
#define mlog_early_w(tag, format, ... )   ESP_EARLY_LOGW(tag, format, ##__VA_ARGS__)
#define mlog_early_i(tag, format, ... )   ESP_EARLY_LOGI(tag, format, ##__VA_ARGS__)
#define mlog_early_d(tag, format, ... )   ESP_EARLY_LOGD(tag, format, ##__VA_ARGS__)
#define mlog_early_v(tag, format, ... )   ESP_EARLY_LOGV(tag, format, ##__VA_ARGS__)

// log interval

#define mlog_int_e(tag, ms, format, ... ) \
do {\
    static int64_t timelast = 0; \
    if ( esp_timer_get_time() - timelast > ms * 1000  ) { \
        timelast = esp_timer_get_time();\
        ESP_LOGE(tag, format, ##__VA_ARGS__); \
    } \
} while(0)\

#define mlog_int_w(tag, ms, format, ... ) \
do {\
    static int64_t timelast = 0; \
    if ( esp_timer_get_time() - timelast > ms * 1000  ) { \
        timelast = esp_timer_get_time();\
        ESP_LOGW(tag, format, ##__VA_ARGS__); \
    } \
} while(0)\

#define mlog_int_i(tag, ms, format, ... ) \
do {\
    static int64_t timelast = 0; \
    if ( esp_timer_get_time() - timelast > ms * 1000  ) { \
        timelast = esp_timer_get_time();\
        ESP_LOGI(tag, format, ##__VA_ARGS__); \
    } \
} while(0)\

#define mlog_int_d(tag, ms, format, ... ) \
do {\
    static int64_t timelast = 0; \
    if ( esp_timer_get_time() - timelast > ms * 1000  ) { \
        timelast = esp_timer_get_time();\
        ESP_LOGD(tag, format, ##__VA_ARGS__); \
    } \
} while(0)\


#define mlog_int_v(tag, ms, format, ... ) \
do {\
    static int64_t timelast = 0; \
    if ( esp_timer_get_time() - timelast > ms * 1000  ) { \
        timelast = esp_timer_get_time();\
        ESP_LOGV(tag, format, ##__VA_ARGS__); \
    } \
} while(0)\


// early log interval

#define mlog_early_int_e(tag, ms, format, ... ) \
do {\
    static int64_t timelast = 0; \
    if ( esp_timer_get_time() - timelast > ms * 1000  ) { \
        timelast = esp_timer_get_time();\
        ESP_EARLY_LOGE(tag, format, ##__VA_ARGS__); \
    } \
} while(0)\

#define mlog_early_int_w(tag, ms, format, ... ) \
do {\
    static int64_t timelast = 0; \
    if ( esp_timer_get_time() - timelast > ms * 1000  ) { \
        timelast = esp_timer_get_time();\
        ESP_EARLY_LOGW(tag, format, ##__VA_ARGS__); \
    } \
} while(0)\

#define mlog_early_int_i(tag, ms, format, ... ) \
do {\
    static int64_t timelast = 0; \
    if ( esp_timer_get_time() - timelast > ms * 1000  ) { \
        timelast = esp_timer_get_time();\
        ESP_EARLY_LOGI(tag, format, ##__VA_ARGS__); \
    } \
} while(0)\

#define mlog_early_int_d(tag, ms, format, ... ) \
do {\
    static int64_t timelast = 0; \
    if ( esp_timer_get_time() - timelast > ms * 1000  ) { \
        timelast = esp_timer_get_time();\
        ESP_EARLY_LOGD(tag, format, ##__VA_ARGS__); \
    } \
} while(0)\

#define mlog_early_int_v(tag, ms, format, ... ) \
do {\
    static int64_t timelast = 0; \
    if ( esp_timer_get_time() - timelast > ms * 1000  ) { \
        timelast = esp_timer_get_time();\
        ESP_EARLY_LOGV(tag, format, ##__VA_ARGS__); \
    } \
} while(0)\

#endif
