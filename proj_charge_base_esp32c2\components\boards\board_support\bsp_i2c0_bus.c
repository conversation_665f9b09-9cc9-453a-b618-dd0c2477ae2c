#include "driver/i2c.h"
#include "driver/gpio.h"
#include "logger.h"
#include <string.h>

// i2c0 gpio
#define I2C0_FREQ          100000
#define I2C0_SCL           GPIO_NUM_0
#define I2C0_SDA           GPIO_NUM_1

#define I2C0_CONFIG_DEFAULT() {                 \
        .mode = I2C_MODE_MASTER,                \
        .sda_io_num = I2C0_SDA,                 \
        .scl_io_num = I2C0_SCL,                 \
        .sda_pullup_en = GPIO_PULLUP_ENABLE,    \
        .scl_pullup_en = GPIO_PULLUP_ENABLE,    \
        .master.clk_speed = I2C0_FREQ,          \
};

int32_t bsp_i2c0_bus_init()
{    
    i2c_config_t conf = I2C0_CONFIG_DEFAULT();
    
    esp_err_t ret = ESP_OK;
    ret |= i2c_param_config(I2C_NUM_0, &conf);
    ret |= i2c_driver_install(I2C_NUM_0, conf.mode, 0, 0, 0);
    return (ret == ESP_OK) ? 0 : -1;
}

 
 