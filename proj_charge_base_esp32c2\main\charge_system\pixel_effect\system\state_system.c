#include "local_device.h"
#include "logger.h" 
#include "pixeleff_system.h"

#include "pixel_waves.h"
#include "state_system.h"

#define tag  "[system-mode]"

static wave_linear_t    wave_value;
static float   	        high_value 	    = 0.0f;
static float   	        mid_value  	    = 0.5f;
static float   	        low_value  	    = 0.0f;
static float 	        breathe_time    = 0.0f;
static float            phase_rise_end  = 0;         
static float            phase_fall_end  = 0;  

static float   	        flicker_time  	= 0.0f;
static float   	        flicker_buty  	= 0.0f;
static float 	        high_end_time 	= 0;
static float 	        low_end_time 	= 0;

static uint8_t          phase           = 0;          
static int8_t           state           = -1;
int32_t system_breathe(float time)
{
    state           = 0;
	breathe_time    = time;
    phase           = 0;
	return 0;
}

int32_t system_breathe_color(float time)
{
    state           = 2;
	breathe_time    = time;
    phase           = 0;
	return 0;
}

int32_t system_flicker(float time ,float buty)
{
    state        = 1;
	flicker_time = time;
	flicker_buty = buty;
	phase		    = 0;
	return 0;
}

void system_fixed_value(float value)
{
    state = 3;
    pixs_system()->hsv.v = value;
}


static void breathe_update(void)
{
    float time = dev_charge()->time.get_seconds(); 
    
    switch (phase)
    {
        case 0:
            wave_point_t src = {time , high_value};
            wave_point_t dst = {time + breathe_time/2,mid_value};
            pixwaves_linear_init(&wave_value, &src, &dst);
            phase_rise_end = time+ breathe_time/2;
            phase = 1;
            break;
        case 1:
            if(time > phase_rise_end)
            {
                wave_point_t src = {time, 	mid_value};
                wave_point_t dst = {time + breathe_time/2,low_value};
                pixwaves_linear_init(&wave_value, &src, &dst);
                phase_fall_end = time+ breathe_time/2;
                phase = 2;
            }
            break;
        case 2:
            if(time > phase_fall_end)
            {
                phase = 0;
            }
            break;
        
        default:
            break;
    }   
	float value = pixwaves_linear_value(&wave_value);
	pixs_system()->hsv.v = value;
}


static void flicker_update(void)
{
	float time = dev_charge()->time.get_seconds();
	
	switch (phase)
	{
		case 0:
			high_end_time = time+ flicker_time*flicker_buty;
			low_end_time = time + flicker_time;
			pixs_system()->hsv.v = 0.1f;
			phase  = 1;
			break;
		case 1:
			if(time > high_end_time)
			{
				pixs_system()->hsv.v = 0;
				phase = 2;
			}
			break;
		case 2:
			if(time > low_end_time)
			{
				phase = 0;
			}
			break;	
		default:
			
			
			break;
	}
}


int32_t system_colour(colour_t colour) 
{
    if (colour < COLOUR_MAX) 
    {
        pixs_system()->hsv.h = g_colour_table[colour].h;
        pixs_system()->hsv.s = g_colour_table[colour].s;
    } 
    else 
    {
        pixs_system()->hsv.h = 0.0f;
        pixs_system()->hsv.s = 0.0f;
    }
    return 0;
}


static void breathe_color_update(void)
{
    static uint8_t colour = 0;
    float time = dev_charge()->time.get_seconds(); 

    switch (phase)
    {
        case 0:
            wave_point_t src = {time , high_value};
            wave_point_t dst = {time + breathe_time/2,mid_value};
            pixwaves_linear_init(&wave_value, &src, &dst);
            phase_rise_end = time+ breathe_time/2;
            phase = 1;
            break;
        case 1:
            if(time > phase_rise_end)
            {
                wave_point_t src = {time, 	mid_value};
                wave_point_t dst = {time + breathe_time/2,low_value};
                pixwaves_linear_init(&wave_value, &src, &dst);
                phase_fall_end = time+ breathe_time/2;
                phase = 2;
            }
            break;
        case 2:
            if(time > phase_fall_end)
            {
                phase = 0;
                colour++;
                if(colour >= COLOUR_MAX)
                {
                    colour = 0;
                }
                system_colour(colour);
            }
            break;
        
        default:
            break;
    }   
	float value = pixwaves_linear_value(&wave_value);
	pixs_system()->hsv.v = value;
}



void system_update(void)
{
    switch (state)
    {
        case 0: 
            breathe_update();
            break;
        case 1: 
            flicker_update();
            break;
        case 2: 
            breathe_color_update();
            break;
        
        default:

            break;
    }
}