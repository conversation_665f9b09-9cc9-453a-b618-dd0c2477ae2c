#include "pad_trigger.h"
#include <string.h>

static uint8_t find_min(uint8_t *data, uint32_t len)
{
    uint8_t min = 255;
    for (uint32_t i = 0; i < len; i++)
    {
        if (data[i] < min)
        {
            min = data[i];
        }
    }
    return min;
}

int32_t pad_init(pad_trigger_t *pad, 
				uint8_t thr, 
				uint8_t buflen, 
				pfunc_trig ptrig, 
				pfunc_aftouch ptouch)
{
	pad->pressing 	= 0;
	pad->released 	= 1;
	pad->thr 		= thr;
	pad->bufidx		= 0;
	pad->buflen		= buflen;

	pad->call_trig 		= ptrig;
	pad->call_aftouch 	= ptouch;

	memset(pad->buffer, 0, PADBFU_MAX);
	return 0;
}

void pad_set_thr(pad_trigger_t *pad, uint8_t thr)
{
	pad->thr = thr;
}

int32_t pad_sample(pad_trigger_t *pad, uint8_t value)
{
	pad->release_prev = pad->released;

	if ((pad->released)
	 && (value < pad->thr))
	{
	    pad->pressing = 1;
	    pad->released = 0;
	}

	if (value >= pad->thr)
	{   
	    pad->released	= 1;
	    pad->pressing 	= 0;
	    pad->bufidx 	= 0;
	}	

	if (pad->pressing)
	{
	    pad->buffer[pad->bufidx ++] = value;
	    if (pad->bufidx >= pad->buflen)
	    {
	        pad->pressing = 0;
	        uint8_t velocity = 255 - find_min(pad->buffer, pad->buflen);
	        if (velocity > 127)  velocity = 127;
	        if (pad->call_trig)
	        {
	        	pad->call_trig(velocity);
	        }
	    }
	}	

	if ((pad->released == 0) 
	 && (pad->pressing == 0))
	{
        if (pad->call_aftouch)
        {
        	pad->call_aftouch(255 - value);
        }
	}

#if 0
	if ((pad->released == 1)
	 && (pad->release_prev == 1))
	{
		if (pad->call_release)
        {
        	pad->call_release(0);
        }
	}
#endif
	return 0;
}

void pad_regist_call(pad_trigger_t *pad,
						pfunc_trig p_trig,
						pfunc_release p_release,
						pfunc_aftouch p_aftouch)
{
	pad->call_trig 		= p_trig;
	pad->call_release	= p_release;
	pad->call_aftouch 	= p_aftouch;
}

void pad_regist_call_pressing(pad_trigger_t *pad, pfunc_trig p_trig)
{
	pad->call_trig 		= p_trig;
}

void pad_regist_call_release(pad_trigger_t *pad, pfunc_release p_release)
{
	pad->call_release	= p_release;
}

void pad_regist_call_aftertouch(pad_trigger_t *pad, pfunc_aftouch p_aftouch)
{
	pad->call_aftouch 	= p_aftouch;
}



