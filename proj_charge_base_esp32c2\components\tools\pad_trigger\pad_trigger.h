#ifndef _H_KEY_DETECT_H_
#define _H_KEY_DETECT_H_

#include <stdint.h>

#define PADBFU_MAX 		(50)

typedef void (*pfunc_trig)(uint8_t);
typedef void (*pfunc_release)(uint8_t);
typedef void (*pfunc_aftouch)(uint8_t);

typedef struct
{
	uint8_t 		pressing;
	uint8_t 		released;
	uint8_t 		release_prev;

	uint8_t 		thr;

	uint8_t 		bufidx;
	uint8_t 		buflen;	
	uint8_t 		buffer[PADBFU_MAX];

	pfunc_trig  	call_trig;
	pfunc_release  	call_release;
	pfunc_aftouch  	call_aftouch;
}pad_trigger_t;

#ifdef __cplusplus
 extern "C" {
#endif

int32_t pad_init(pad_trigger_t *pad, 
				uint8_t thr, 
				uint8_t buflen, 
				pfunc_trig ptrig, 
				pfunc_aftouch ptouch);

void pad_set_thr(pad_trigger_t *pad, uint8_t thr);

int32_t pad_sample(pad_trigger_t *pad, uint8_t value);
void pad_regist_call(pad_trigger_t *pad,
						pfunc_trig p_trig,
						pfunc_release p_release,
						pfunc_aftouch p_aftouch);

void pad_regist_call_pressing(pad_trigger_t *pad, pfunc_trig p_trig);
void pad_regist_call_release(pad_trigger_t *pad, pfunc_release p_release);
void pad_regist_call_aftertouch(pad_trigger_t *pad, pfunc_aftouch p_aftouch);

#ifdef __cplusplus
 }
#endif

#endif
