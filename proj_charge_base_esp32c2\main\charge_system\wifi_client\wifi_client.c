#include "tcp_client.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "logger.h"
#include "protocol_tlvc_encrypt.h"
#include "esp_heap_caps.h"
#include "generate_iv.h"
#include "encrypt_keys/key_wifi.h"


#define ltag  "wifi_client"

#define TLVC_BUFLEN  (2048 + 128)

static uint8_t *tlv_buffer = NULL;
static protocol_tlvc_enc_t  tlv_tcpip;
static TaskHandle_t wifi_auto_task_handle = NULL;

static void callback_tlv_terminal(uint8_t *data, uint32_t len);
static void callback_tlv_charging(uint8_t *data, uint32_t len);
int32_t wifi_client_data_tran(uint8_t *data, uint32_t len);
 
static element_str_tlvc_t msg_elems[] =
{
    {"terminal", 8, callback_tlv_terminal},
    {"charging", 8, callback_tlv_charging},
};

typedef struct {
    uint8_t wifi_ap_num;
    wifi_account_t *wifi_ap_list;
}wifi_scan_result_t;

static wifi_scan_result_t wifi_scan;

static void callback_tlv_terminal(uint8_t *data, uint32_t len)
{               
    // dispatch_cmds_call(data, len, wifi_client_data_tran);
}

static void callback_tlv_charging(uint8_t *data, uint32_t len)
{               
    if ((data[0] == 0x00)  && (data[1] == 0x01))
    {
         mlog_i(ltag, "vTaskDelete(wifi_auto_task_handle)!\r\n");
        vTaskDelete(wifi_auto_task_handle);
    }
}

protocol_tlvc_enc_t* wifi_tlvc()
{
    return &tlv_tcpip;
}

static int32_t regist_tlv_protocol()
{    
    tlv_buffer = heap_caps_malloc(TLVC_BUFLEN, MALLOC_CAP_INTERNAL);
    if (tlv_buffer == NULL)
    {
        mlog_e(ltag, "malloc failed : %s %d", __FILE__, __LINE__);
        return -1;
    }

    uint32_t msg_elem_number = sizeof(msg_elems) / sizeof (element_str_tlvc_t);
    protocol_tlvc_encrypt_init(&tlv_tcpip, false, key_wifi, ENCRYPT_KEY_LENGTH, encrypt_generate_iv);
    return protocol_tlvc_encrypt_string_regist(&tlv_tcpip,
                                                '$',
                                                'A',
                                                msg_elems,
                                                msg_elem_number,
                                                tlv_buffer,
                                                TLVC_BUFLEN);
}

int32_t wifi_client_data_tran(uint8_t *data, uint32_t len)
{
    return tcp_client_send(data, len);
}

static void wifi_client_recv(uint8_t *data, uint32_t len)
{
#if 1
    printf("wifi recieved_len = %d:\r\n", len);
    for (uint32_t i = 0; i < len; i++)
    {
        printf("%02X ", data[i]);
    }
    printf("\r\n");
#endif
    // test
    // if (len > 0 && data[0] == 'w' && data[1] == 4)
    // {
    //     vTaskDelete(wifi_auto_task_handle);
    // }
    protocol_tlvc_encrypt_decode(&tlv_tcpip, data, len);
}

/**
 * @brief Task to automatically connect to a list of Wi-Fi APs, send data, and disconnect.
 */
static void wifi_auto_connect_task(void *pvParameters)
{
     mlog_i(ltag, "wifi_auto_connect_task started.");

    while(1)
    {
        if (wifi_scan.wifi_ap_num == 0) {
             mlog_i(ltag, "No target APs configured (target_ap_num is 0). Exiting task.");
            vTaskDelete(NULL);
            return;
        }
    
        for (uint8_t i = 0; i < wifi_scan.wifi_ap_num; i++) {
             mlog_i(ltag, "Processing AP #%u: SSID: %s", i + 1, wifi_scan.wifi_ap_list[i].ssid);
    
            if (try_connect_to_ap((const char*)wifi_scan.wifi_ap_list[i].ssid, (const char*)wifi_scan.wifi_ap_list[i].password)) {
                vTaskDelay(pdMS_TO_TICKS(500)); 
                 mlog_i(ltag, "Successfully connected to %s.", wifi_scan.wifi_ap_list[i].ssid);
                
                // Send test data packet
                uint8_t test_packet[128];
                uint8_t test_value[6] = {0x00, 0x01};
                int32_t packlen = protocol_tlvc_encrypt_pack(&tlv_tcpip,'$', 'M', "charging", 8, test_value, 2, test_packet, 128);
                for (int8_t j = 0; j < 5; j++)
                {
                    wifi_client_data_tran(test_packet, packlen);
                    vTaskDelay(pdMS_TO_TICKS(100)); 
                }
                
                // Wait a bit after sending data (optional)
                vTaskDelay(pdMS_TO_TICKS(2000)); // e.g., 2 seconds
    
                 mlog_i(ltag, "Disconnecting from %s.", wifi_scan.wifi_ap_list[i].ssid);
                tcp_client_stop();
            } else {
                 mlog_e(ltag, "Failed to connect to %s. Moving to next AP.", wifi_scan.wifi_ap_list[i].ssid);
            }
    
            // Delay before trying the next AP (e.g., 5 seconds)
             mlog_i(ltag, "Waiting before processing next AP...");
            vTaskDelay(pdMS_TO_TICKS(5000)); 
        }
    
         mlog_i(ltag, "Finished processing all target APs. wifi_auto_connect_task ending.");
    }
}

static void wifi_scan_finish(uint8_t num, wifi_account_t *scan_result)
{
    uint8_t i = 0;
    if (num > 0)
    {
        wifi_scan.wifi_ap_num = num;
        mlog_i(ltag, "scan finished. wifi_ap_num = %d\r\n", wifi_scan.wifi_ap_num);
        for (i = 0; i < num; i++) 
        { 
            strlcpy(wifi_scan.wifi_ap_list[i].ssid, (const char *)scan_result[i].ssid, 32);
            strlcpy(wifi_scan.wifi_ap_list[i].password, (const char *)scan_result[i].password, 16);
            mlog_i(ltag, "AP %d: SSID = %s, Password = %s\r\n", i, scan_result[i].ssid, scan_result[i].password);
        }
        // create connect ap task
        if (wifi_auto_task_handle == NULL)
        {
            xTaskCreate(wifi_auto_connect_task, "wifi_auto_conn_task", 4096, NULL, 5, &wifi_auto_task_handle);
        }
    }
}

void wifi_scan_init(void)
{
    wifi_scan.wifi_ap_num = 0;
    wifi_scan.wifi_ap_list = NULL;
    wifi_scan.wifi_ap_list = heap_caps_malloc(sizeof(wifi_account_t)*MAX_AP_NUM, MALLOC_CAP_INTERNAL);
    if (wifi_scan.wifi_ap_list == NULL)
    {
        mlog_e(ltag, "malloc failed : %s %d", __FILE__, __LINE__);
    }
    else
    {
        tcp_client_register_scan_callback(wifi_scan_finish);
    }
}

int32_t wifi_client_init(void)
{
    if (regist_tlv_protocol() < 0)
    {
        mlog_e(ltag, "regist_tlv_protocol failed!\r\n");
        return -1;
    }
    wifi_scan_init();
    tcp_client_register_recv_callback(wifi_client_recv);
    tcp_client_start("LiberAP-", "");

    return 0;
}

 int32_t wifi_client_start(void)
{
    return 0;
}

int32_t wifi_client_stop(void)
{
    return tcp_client_stop();
}