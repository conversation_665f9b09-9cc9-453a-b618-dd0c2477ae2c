#include "key_detect.h"

void key_log_prev(user_iter_key_t *key)
{
	key->pressed_pre = key->pressed_cur;
}

void key_pressing_detect(user_iter_key_t *key, float time_cur)
{
	if (key->pressed_cur && !key->pressed_pre)
	{
		key->pressing = 1;
		key->pressing_time = time_cur;
	}
	else
	{
		key->pressing = 0;
	}

	if (!key->pressed_cur && key->pressed_pre)
	{
		key->releasing = 1;
	}
	else
	{
		key->releasing = 0;
	}

	if (key->pressed_cur)
	{
		key->pressed_time = time_cur - key->pressing_time;
	}
	else
	{
		key->pressed_time = 0;
	}
}




