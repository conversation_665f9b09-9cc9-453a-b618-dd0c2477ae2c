#ifndef _H_TYPE_BATTERY_H
#define _H_TYPE_BATTERY_H

#include <stdint.h>
 
typedef struct
{
    int32_t (*init)(void);
    int32_t (*update)(void);
    int32_t (*is_charging)(void);
    float   (*get_vol)(void);
    float   (*get_adc)(void);    
    float   (*max_vol)(void);
    float   (*warning_vol)(void);
    float   (*emergency_vol)(void);
    int32_t (*set_scale)(float);    
}dev_adc_battery_t;

#endif
