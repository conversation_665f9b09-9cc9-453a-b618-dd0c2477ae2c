#ifndef __DRV_INA226_H
#define __DRV_INA226_H

#include <stdint.h>
#include "esp_err.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/i2c.h"
#include "driver/i2c_master.h"

#define I2C_MASTER_SCL_IO 0       // SCL 引脚
#define I2C_MASTER_SDA_IO 1       // SDA 引脚
#define I2C_MASTER_NUM I2C_NUM_0   // I2C 端口号
#define I2C_MASTER_FREQ_HZ 400000  // I2C 时钟频率 (400kHz)
#define I2C_MASTER_TIMEOUT_MS 1000 // I2C 超时时间 (ms)


// INA226 地址和寄存器定义
#define INA226_ADDR1 0x40          // INA226 I2C 地址
#define CFG_REG 0x00               // 配置寄存器
#define SV_REG 0x01                // 分流电压寄存器
#define BV_REG 0x02                // 总线电压寄存器
#define PWR_REG 0x03               // 功率寄存器
#define CUR_REG 0x04               // 电流寄存器
#define CAL_REG 0x05               // 校准寄存器

// INA226 参数定义
#define MODE_INA226 0x4327         // 配置工作模式
#define INA226_VAL_LSB 2.03f        // 分流电压 LSB，单位 uV
#define Voltage_LSB 1.2322f          // 总线电压 LSB，单位 mV
#define CURRENT_LSB 0.0896f          // 电流 LSB，单位 mA
#define POWER_LSB (24.34* CURRENT_LSB) // 功率 LSB，单位 mW
#define CAL  0x1638                   // 校准寄存器值

typedef struct 
{
    float bus_voltage;      // 总线电压（mV）
    float shunt_voltage;    // 分流电压（uV）
    float current;          // 电流（mA）
    float power;            // 功率（mW）
} ina226_data_t;


void i2c_master_init(void);
void ina226_init(void);
esp_err_t ina226_write_register(uint8_t reg, uint16_t data);
esp_err_t ina226_read_register(uint8_t reg, uint16_t *data);
void ina226_update();
// 获取总线电压
float ina226_get_voltage(void);
// 获取分流电压
float ina226_get_shunt_voltage(void);
// 获取电流
float ina226_get_current(void);
// 获取功率
float ina226_get_power(void);
#endif
