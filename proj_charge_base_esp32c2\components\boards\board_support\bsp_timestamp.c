#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

int32_t bsp_timestamp_init()
{
	return 0;
}

uint64_t bsp_get_micros()
{
	return (uint64_t)esp_timer_get_time();
}

uint32_t bsp_get_millis()
{
	return (uint32_t)(esp_timer_get_time() / 1000);
}

float bsp_get_second()
{
	return esp_timer_get_time() / 1000000.0f;
}

void bsp_block_delayus(uint32_t us)
{
	uint64_t tin = bsp_get_micros();
	while (bsp_get_micros() - tin < us);
}

void bsp_block_delayms(uint32_t ms)
{
	uint32_t tin = bsp_get_millis();
	while (bsp_get_millis() - tin < ms);
}

void bsp_rtos_delayms(uint32_t ms)
{
	vTaskDelay(ms / portTICK_RATE_MS);
}
