#include "nvs_flash.h"
#include "nvs.h"

#include "logger.h"

#define tag "[BSP]nvs"

nvs_handle_t mynvs_handle;

int32_t bsp_nvs_init(void)
{
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND)
    {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    esp_err_t err = nvs_open("nvs", NVS_READWRITE, &mynvs_handle);
    if (err != ESP_OK)
    {
        mlog_e(tag, "Error (%s) opening NVS handle!\n", esp_err_to_name(err));
        return -1;
    }
    return 0;
}

int32_t bsp_nvs_set_str(const char* key, const char* in_value)
{
    if (!mynvs_handle)
    {
        return -1;
    }

    esp_err_t ret1, ret2;
    ret1 = nvs_set_str(mynvs_handle, key, in_value);
    ret2 = nvs_commit(mynvs_handle);

#if 0
    mlog_i(tag, "set str, key = %s, value = %s, ret:%d %d", key, in_value, ret1, ret2);
#endif
    return ((ret1 == ESP_OK) && (ret2 == ESP_OK)) ? 0 : -1;
}

int32_t bsp_nvs_get_str(const char* key, char* out_value)
{
    if (!mynvs_handle)
    {
        return -1;
    }
    esp_err_t ret1, ret2;
    size_t  length = 0;
    ret1 = nvs_get_str(mynvs_handle, key, NULL, &length);
    ret2 = nvs_get_str(mynvs_handle, key, out_value, &length);

#if 0
        mlog_i(tag, "get str, key = %s, value = %s, len = %d, ret:%d %d",
            key, out_value, length, ret1, ret2);
#endif
    return ((ret1 == ESP_OK) && (ret2 == ESP_OK)) ? 0 : -1;
}

int32_t bsp_nvs_set_u32(const char* key, uint32_t in_value)
{
    if (!mynvs_handle)
    {
        return -1;
    }

    esp_err_t ret1, ret2;
    ret1 = nvs_set_u32(mynvs_handle, key, in_value);
    ret2 = nvs_commit(mynvs_handle);

#if 0
        mlog_i(tag, "set u32, key = %s, value = %d, ret:%d %d",
            key, in_value, ret1, ret2);
#endif
    return ((ret1 == ESP_OK) && (ret2 == ESP_OK)) ? 0 : -1;
}

int32_t bsp_nvs_get_u32(const char* key, uint32_t* out_value)
{
    if (!mynvs_handle)
    {
        return -1;
    }
    esp_err_t ret1 = nvs_get_u32(mynvs_handle, key, out_value);
#if 0
        mlog_i(tag, "get u32, key = %s, value = %d, ret:%d",
            key, *out_value, ret1);
#endif
    return (ret1 == ESP_OK) ? 0 : -1;
}


int32_t bsp_nvs_set_i8(const char* key, int8_t in_value)
{
    if (!mynvs_handle)
    {
        return -1;
    }

    esp_err_t ret1, ret2;
    ret1 = nvs_set_i8(mynvs_handle, key, in_value);
    ret2 = nvs_commit(mynvs_handle);

#if 0
        mlog_i(tag, "set i8, key = %s, value = %d, ret:%d %d",
            key, in_value, ret1, ret2);
#endif
    return ((ret1 == ESP_OK) && (ret2 == ESP_OK)) ? 0 : -1;
}

int32_t bsp_nvs_get_i8(const char* key, int8_t* out_value)
{
    if (!mynvs_handle)
    {
        return -1;
    }
    esp_err_t ret1 = nvs_get_i8(mynvs_handle, key, out_value);
#if 0
        mlog_i(tag, "get i8, key = %s, value = %d, ret:%d",
            key, *out_value, ret1);
#endif
    return (ret1 == ESP_OK) ? 0 : -1;
}

int32_t bsp_nvs_set_u8(const char* key, uint8_t in_value)
{
    if (!mynvs_handle)
    {
        return -1;
    }

    esp_err_t ret1, ret2;
    ret1 = nvs_set_u8(mynvs_handle, key, in_value);
    ret2 = nvs_commit(mynvs_handle);

#if 0
        mlog_i(tag, "set i8, key = %s, value = %d, ret:%d %d",
            key, in_value, ret1, ret2);
#endif
    return ((ret1 == ESP_OK) && (ret2 == ESP_OK)) ? 0 : -1;
}

int32_t bsp_nvs_get_u8(const char* key, uint8_t* out_value)
{
    if (!mynvs_handle)
    {
        return -1;
    }
    esp_err_t ret1 = nvs_get_u8(mynvs_handle, key, out_value);
#if 0
        mlog_i(tag, "get i8, key = %s, value = %d, ret:%d",
            key, *out_value, ret1);
#endif
    return (ret1 == ESP_OK) ? 0 : -1;
}

int32_t bsp_nvs_set_i16(const char* key, int16_t in_value)
{
    if (!mynvs_handle)
    {
        return -1;
    }

    esp_err_t ret1, ret2;
    ret1 = nvs_set_i16(mynvs_handle, key, in_value);
    ret2 = nvs_commit(mynvs_handle);
#if 0
        mlog_i(tag, "set i16, key = %s, value = %d, ret:%d %d",
            key, in_value, ret1, ret2);
#endif

    return ((ret1 == ESP_OK) && (ret2 == ESP_OK)) ? 0 : -1;
}

int32_t bsp_nvs_get_i16(const char* key, int16_t* out_value)
{
    if (!mynvs_handle)
    {
        return -1;
    }
    esp_err_t ret1 = nvs_get_i16(mynvs_handle, key, out_value);
#if 0
        mlog_i(tag, "get i16, key = %s, value = %d, ret:%d",
            key, *out_value, ret1);
#endif
    return (ret1 == ESP_OK) ? 0 : -1;
}

int32_t bsp_nvs_set_u16(const char* key, uint16_t in_value)
{
    if (!mynvs_handle)
    {
        return -1;
    }

    esp_err_t ret1, ret2;
    ret1 = nvs_set_u16(mynvs_handle, key, in_value);
    ret2 = nvs_commit(mynvs_handle);
#if 0
        mlog_i(tag, "set u16, key = %s, value = %d, ret:%d %d",
            key, in_value, ret1, ret2);
#endif
    return ((ret1 == ESP_OK) && (ret2 == ESP_OK)) ? 0 : -1;
}

int32_t bsp_nvs_get_u16(const char* key, uint16_t* out_value)
{
    if (!mynvs_handle)
    {
        return -1;
    }
    esp_err_t ret1 = nvs_get_u16(mynvs_handle, key, out_value);
#if 0
        mlog_i(tag, "get u16, key = %s, value = %d, ret:%d",
            key, *out_value, ret1);
#endif
    return (ret1 == ESP_OK) ? 0 : -1;
}

int32_t bsp_nvs_set_blob(const char* key, uint8_t* data , uint32_t len)
{
    if (!mynvs_handle)
    {
        return -1;
    }

    esp_err_t ret1, ret2;
    ret1 = nvs_set_blob(mynvs_handle, key, data, (size_t)len);
    ret2 = nvs_commit(mynvs_handle);
#if 0
        mlog_i(tag, "set blob, key = %s, len = %d, ret:%d %d",
            key, len, ret1, ret2);
#endif
    return ((ret1 == ESP_OK) && (ret2 == ESP_OK)) ? 0 : -1;
}

int32_t bsp_nvs_get_blob(const char* key, uint8_t* data, uint32_t *len)
{
    if (!mynvs_handle)
    {
        return -1;
    }

    size_t length = *len;
    esp_err_t ret1 = nvs_get_blob(mynvs_handle, key, data, &length);

#if 0
        mlog_i(tag, "get blob[%s], inlen:%d, outlen:%d, ret:%x",
            key, *len, length, ret1);
#endif

    if (ret1 == ESP_OK)
    {
        *len = length;
        return 0;
    }    
    return -1;
}

