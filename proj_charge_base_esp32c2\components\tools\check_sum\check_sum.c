#include <stdint.h>

void check_sum(uint8_t *data, uint32_t len, uint8_t* ch_a, uint8_t *ch_b)
{
	uint8_t ck_a = 0;
	uint8_t ck_b = 0;

	for (uint16_t i = 0; i < len; i++)
	{
		ck_a += data[i];
		ck_b += ck_a;
	}

	*ch_a = ck_a;
	*ch_b = ck_b;
}

void check_sum_continue(uint8_t *data, uint32_t len, uint8_t* ch_a, uint8_t *ch_b)
{
	uint8_t ck_a = *ch_a;
	uint8_t ck_b = *ch_b;

	for (uint16_t i = 0; i < len; i++)
	{
		ck_a += data[i];
		ck_b += ck_a;
	}

	*ch_a = ck_a;
	*ch_b = ck_b;
}






