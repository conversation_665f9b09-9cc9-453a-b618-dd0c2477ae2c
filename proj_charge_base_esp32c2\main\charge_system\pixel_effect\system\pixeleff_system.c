#include "local_device.h"
#include "logger.h" 
#include "pixeleff_system.h"
#include "hsv_to_rgb.h"

 #include "state_system.h"
#define tag  "[EFF-FRET-SYSTEM]"

pixels_system_t* pixs_system()
{
	static pixels_system_t  	pix_system;
	return &pix_system;
}
int32_t pixeleff_system_init()
{

	return 0;
}

void pixeleff_system_update()
{
	system_update();
	hsv_to_rgb(pixs_system()->hsv.h,
	pixs_system()->hsv.s,
	pixs_system()->hsv.v,
	&pixs_system()->rgb.r,
	&pixs_system()->rgb.g,
	&pixs_system()->rgb.b);
	dev_charge()->pixel_onboard.set(0, pixs_system()->rgb.r,
								pixs_system()->rgb.g,
								pixs_system()->rgb.b);


}

int32_t pixeleff_system_breathe(float time,colour_t colour)
{
	system_colour(colour);
	system_breathe(time);
	return 0;
}

int32_t pixeleff_system_flicker(float time,float buty,colour_t colour)
{
	system_colour(colour);
	system_flicker(time,buty);
	return 0;
}
int32_t pixeleff_system_colour(colour_t colour,float value)
{
	system_fixed_value(value);
	system_colour(colour);
	return 0;
}

int32_t pixeleff_system_breathe_color(float time)
{
	system_breathe_color(time);
	return 0;
}

