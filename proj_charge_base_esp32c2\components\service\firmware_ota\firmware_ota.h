#ifndef _H_FIRMWARE_OTA_H
#define _H_FIRMWARE_OTA_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

int32_t firmware_ota_start(void);
int32_t firmware_ota_write(uint8_t *data, uint32_t len);
int32_t firmware_ota_ending(void);;
int32_t firmware_ota_get_buildtime(char *data, uint32_t *len);
int32_t firmware_ota_get_version(uint32_t *version);
int32_t firmware_ota_get_proj_name(char *data, uint32_t *len);

#ifdef __cplusplus
}
#endif

#endif  
