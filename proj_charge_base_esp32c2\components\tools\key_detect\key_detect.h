#ifndef _H_KEY_DETECT_H_
#define _H_KEY_DETECT_H_

#include <stdint.h>

typedef struct
{
	int32_t 	pressed_cur;
	int32_t 	pressed_pre;
	int32_t 	pressing;
	int32_t 	releasing;
	int32_t 	double_click;
	float 		pressing_time;
	float 		pressed_time;
	float 		pre_pressing_time;
	float 		dt_pressing_time;
}user_iter_key_t;

#ifdef __cplusplus
 extern "C" {
#endif

void key_log_prev(user_iter_key_t *key);
void key_pressing_detect(user_iter_key_t *key, float time_cur);

#ifdef __cplusplus
 }
#endif

#endif
