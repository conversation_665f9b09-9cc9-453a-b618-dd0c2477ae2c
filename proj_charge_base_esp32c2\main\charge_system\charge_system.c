#include <string.h>
#include "logger.h"
#include "firmware_ota.h"
#include "i2c_device.h"
#include "local_device.h"
#include "wifi_client.h"
#include "pixeleff_manager.h"
#include "pixel_common.h"
#include "pixeleff_system.h"
#include "drv_ina226.h"
#include "drv_husb238.h"
#include "charge_manager.h"

#define tag  "[System]"


static void print_system_info()
{
    int32_t uidlen = 6;
    uint8_t uid[6];
    dev_charge()->chip_id.get(uid, &uidlen);
    mlog_i(tag, "uid : %02X%02X%02X%02X%02X%02X", 
            uid[0], uid[1], uid[2], uid[3], uid[4], uid[5]);

    char fw_buildtime[128];
    uint32_t len = 128;
    firmware_ota_get_buildtime(fw_buildtime, &len);
    mlog_w(tag, "firmware time: [%s]", fw_buildtime);    
}

uint16_t voltage[6];
float current[6];
uint8_t count = 0;


/*
0: 插入，未连接
1: 未插入，未连接
2: 插入，已连接
3: 未插入，已连接
*/
void detection_handle()
{
    static uint8_t state;
    static uint8_t charge_phase = 0; // 0: 未开始, 1: 已达到高功率, 2: 充电完成
    uint8_t new_state;
    float power = ina226_get_power();
    // 判断连接状态
    if (power < 100) 
    {
        new_state = 0; // 拔出来
        charge_phase = 0; // 重置充电阶段
    } 
    else 
    {
        // 充电阶段状态机
        switch (charge_phase) 
        {
            case 0: // 未开始充电阶段
                if (power >= 10000) 
                {
                    charge_phase = 1; // 进入高功率充电阶段
                }
                new_state = 2; // 充电中
                break;

            case 1: // 高功率充电阶段
                if (power <= 2800) 
                {
                    charge_phase = 2; // 充电完成
                    new_state = 1; // 充电完成状态
                } 
                else 
                {
                    new_state = 2; // 仍在充电中
                }
                break;

            case 2: // 充电完成阶段
                // 一旦充电完成，保持完成状态，除非重新拔插
                new_state = 1; // 充电完成
                break;

            default:
                charge_phase = 0;
                new_state = 2;
                break;
        }
    }
    if (new_state != state)
    {
        state = new_state;
        if (state == 0) //拔出来
        {
            if(count > 0)
            {
                drv_husb238_selvoltage(PDO_5V);
            }
            pixeleff_system_colour(WHITE, 0.5);
        }
        else if (state == 1) //充电完成
        {
            pixeleff_system_colour(GREEN, 0.5);
        }
        else if (state == 2) //充电开始
        {
            drv_husb238_getcapabilities(voltage, current);
            pixeleff_system_breathe(2.0,WHITE);
        }
    }
}


int32_t charge_system_init() 
{
    print_system_info();

    int32_t ret = 0;

    ret |= pixeleff_manager_init();
    ret |= i2c_device_init();
    ret |= charge_manager_init();
   // ret |= wifi_client_init();
    ret |= pixeleff_system_colour(WHITE, 0.5);

    count = drv_husb238_getcapabilities(voltage, current);
    if(count > 0)
    {
        drv_husb238_selvoltage(PDO_5V);
        for (int i = 0; i < count; i++)
        {
            mlog_i(tag, "voltage: %d, current: %f", voltage[i], current[i]);
        }
    }
    return ret;
}

int32_t charge_system_update()
{
    static uint32_t tick_tcp = 0;

    charge_manager_update();
    pixeleff_manager_update();
	return 0;
}

