#include <string.h>
#include "logger.h"
#include "firmware_ota.h"
#include "i2c_device.h"
#include "local_device.h"
#include "wifi_client.h"
#include "pixeleff_manager.h"
#include "pixel_common.h"
#include "pixeleff_system.h"
#include "drv_ina226.h"
#include "drv_husb238.h"
#include "charge_manager.h"

#define tag  "[System]"


static void print_system_info()
{
    int32_t uidlen = 6;
    uint8_t uid[6];
    dev_charge()->chip_id.get(uid, &uidlen);
    mlog_i(tag, "uid : %02X%02X%02X%02X%02X%02X", 
            uid[0], uid[1], uid[2], uid[3], uid[4], uid[5]);

    char fw_buildtime[128];
    uint32_t len = 128;
    firmware_ota_get_buildtime(fw_buildtime, &len);
    mlog_w(tag, "firmware time: [%s]", fw_buildtime);    
}



int32_t charge_system_init()
{
    print_system_info();

    int32_t ret = 0;

    ret |= pixeleff_manager_init();
    ret |= i2c_device_init();
    ret |= charge_manager_init();  // charge_manager_init 现在会处理PD能力检测
   // ret |= wifi_client_init();
    ret |= pixeleff_system_colour(WHITE, 0.5);

    return ret;
}

int32_t charge_system_update()
{
    static uint32_t tick_tcp = 0;

    charge_manager_update();
    pixeleff_manager_update();
	return 0;
}

