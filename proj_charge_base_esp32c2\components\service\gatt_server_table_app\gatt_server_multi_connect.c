/*
 * SPDX-FileCopyrightText: 2021-2024 Espressif Systems
 *
 * SPDX-License-Identifier: Unlicense OR CC0-1.0
 */

#include <stdio.h>
#include <string.h>
#include <inttypes.h>

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "freertos/timers.h"

#include "esp_system.h"
#include "esp_log.h"
#include "nvs_flash.h"
#include "esp_bt.h"

#include "esp_gap_ble_api.h"
#include "esp_gatts_api.h"
#include "esp_bt_defs.h"
#include "esp_bt_main.h"
#include "esp_gatt_common_api.h"
#include "gatt_server_multi_connect.h"
#include "sdkconfig.h"


enum {
    IDX_SVC,
    IDX_CHAR_A,
    IDX_CHAR_VAL_A,
    IDX_CHAR_CFG_A,

    IDX_CHAR_B,
    IDX_CHAR_VAL_B,
    ID<PERSON>_CHAR_C,
    IDX_CHAR_VAL_C,

    HRS_IDX_NB
};

#define GATTS_TABLE_TAG              "SEC_GATTS_DEMO"

#define HEART_PROFILE_NUM            1
#define HEART_PROFILE_APP_IDX        0
#define ESP_HEART_RATE_APP_ID        0x55
#define HEART_RATE_SVC_INST_ID       0

#define ADV_CONFIG_FLAG             (1 << 0)
#define SCAN_RSP_CONFIG_FLAG        (1 << 1)

// ---------- Extended Advertising Parameters ----------
#define EXT_ADV_HANDLE_1             0
#define NUM_EXT_ADV_SET              1

// Extended Advertising Duration and Max Events
#define EXT_ADV_DURATION             0
#define EXT_ADV_MAX_EVENTS           0

// GATT characteristic value length max
#define GATTS_DEMO_CHAR_VAL_LEN_MAX  500

#ifndef MIN
#define MIN(a, b) (((a) < (b)) ? (a) : (b))
#endif

// ---------- GATT Attribute Database ----------
static uint16_t profile_handle_table[HRS_IDX_NB];

// UUIDs for the service and characteristics
static const uint16_t GATTS_SERVICE_UUID_TEST      = 0x00FF;
static const uint16_t GATTS_CHAR_UUID_TEST_A       = 0xA101;
static const uint16_t GATTS_CHAR_UUID_TEST_B       = 0xA102;
static const uint16_t GATTS_CHAR_UUID_TEST_C       = 0xA103;

static const uint16_t primary_service_uuid         = ESP_GATT_UUID_PRI_SERVICE;
static const uint16_t character_declaration_uuid   = ESP_GATT_UUID_CHAR_DECLARE;
static const uint16_t character_client_config_uuid = ESP_GATT_UUID_CHAR_CLIENT_CONFIG;

static const uint8_t char_prop_read                = ESP_GATT_CHAR_PROP_BIT_READ;
static const uint8_t char_prop_write               = ESP_GATT_CHAR_PROP_BIT_WRITE;
static const uint8_t char_prop_notify              = ESP_GATT_CHAR_PROP_BIT_NOTIFY;
// static const uint8_t char_prop_read_write_notify   = ESP_GATT_CHAR_PROP_BIT_READ |
//                                                      ESP_GATT_CHAR_PROP_BIT_WRITE |
//                                                      ESP_GATT_CHAR_PROP_BIT_NOTIFY;

static const uint8_t heart_measurement_ccc[2]      = {0x00, 0x00};
static const uint8_t char_value[4]                 = {0x11, 0x22, 0x33, 0x44};

#define CHAR_DECLARATION_SIZE       (sizeof(uint8_t))
#define SVC_INST_ID                 0

// ---------- GATT Database Definition ----------
static const esp_gatts_attr_db_t gatt_db[HRS_IDX_NB] =
{
    // Service Declaration
    [IDX_SVC]        =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, (uint8_t *)&primary_service_uuid, ESP_GATT_PERM_READ,
      sizeof(uint16_t), sizeof(GATTS_SERVICE_UUID_TEST), (uint8_t *)&GATTS_SERVICE_UUID_TEST}},

    // Characteristic Declaration A
    [IDX_CHAR_A]     =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, (uint8_t *)&character_declaration_uuid, ESP_GATT_PERM_READ,
      CHAR_DECLARATION_SIZE, CHAR_DECLARATION_SIZE, (uint8_t *)&char_prop_notify}},

    // Characteristic Value A
    [IDX_CHAR_VAL_A] =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, (uint8_t *)&GATTS_CHAR_UUID_TEST_A, ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE,
      GATTS_DEMO_CHAR_VAL_LEN_MAX, sizeof(char_value), (uint8_t *)char_value}},

    // Client Characteristic Configuration Descriptor (CCC)
    [IDX_CHAR_CFG_A]  =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, (uint8_t *)&character_client_config_uuid, ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE,
      sizeof(uint16_t), sizeof(heart_measurement_ccc), (uint8_t *)heart_measurement_ccc}},

    // Characteristic Declaration B
    [IDX_CHAR_B]      =
    {{ESP_GATT_RSP_BY_APP}, {ESP_UUID_LEN_16, (uint8_t *)&character_declaration_uuid, ESP_GATT_PERM_READ,
      CHAR_DECLARATION_SIZE, CHAR_DECLARATION_SIZE, (uint8_t *)&char_prop_read}},

    // Characteristic Value B
    [IDX_CHAR_VAL_B]  =
    {{ESP_GATT_RSP_BY_APP}, {ESP_UUID_LEN_16, (uint8_t *)&GATTS_CHAR_UUID_TEST_B, ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE,
      GATTS_DEMO_CHAR_VAL_LEN_MAX, sizeof(char_value), (uint8_t *)char_value}},

    // Characteristic Declaration C
    [IDX_CHAR_C]      =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, (uint8_t *)&character_declaration_uuid, ESP_GATT_PERM_READ,
      CHAR_DECLARATION_SIZE, CHAR_DECLARATION_SIZE, (uint8_t *)&char_prop_write}},

    // Characteristic Value C
    [IDX_CHAR_VAL_C]  =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, (uint8_t *)&GATTS_CHAR_UUID_TEST_C, ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE,
      GATTS_DEMO_CHAR_VAL_LEN_MAX, sizeof(char_value), (uint8_t *)char_value}},
};

#define LOCAL_NAME_POSE      12
#define LOCAL_NAME_LENGTH   (16) // Local name length
// ---------- Extended Advertising Parameters ----------
static uint8_t ext_adv_raw_data_1[32] = {
    0x02, 0x01, 0x06,       // Flags
    0x02, 0x0a, 0x09,       // Tx Power
    0x03, 0x03, 0xff, 0x00, // 16-bit UUID
    // Local Name = "ESP_BLE50_SERVER1"
    0x12, 0x09, 'E','S','P','_','B','L','E','5','0','_','S','E','R','V','E','R','1',
};

// scan response
static uint8_t scan_rsp_data[] = {
    0x03, 0x19, 0x80, 0x01,  // Appearance: Generic Phone
    0x0A, 0xFF, 0x4C, 0x00, 0x10, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00  // Apple专用格式
};

// ---------- Extended Advertising Instance ----------
static esp_ble_gap_ext_adv_t ext_adv[NUM_EXT_ADV_SET] = {
    [0] = {EXT_ADV_HANDLE_1, EXT_ADV_DURATION, EXT_ADV_MAX_EVENTS},
};

static uint8_t adv_config_done       = 0;

// ---------- Extended Advertising Parameters ----------
static esp_ble_gap_ext_adv_params_t ext_adv_params_2M_1 = {
    .type = ESP_BLE_GAP_SET_EXT_ADV_PROP_CONNECTABLE,
    .interval_min = 0x20,
    .interval_max = 0x30,
    .channel_map = ADV_CHNL_ALL,
    .filter_policy = ADV_FILTER_ALLOW_SCAN_ANY_CON_ANY,
    .primary_phy = ESP_BLE_GAP_PHY_1M,
    .max_skip = 0,
    .secondary_phy = ESP_BLE_GAP_PHY_2M,
    .sid = 0,
    .scan_req_notif = true,
    .own_addr_type = BLE_ADDR_TYPE_PUBLIC,
    .tx_power = 9,
    // .tx_power = EXT_ADV_TX_PWR_NO_PREFERENCE,
};
// ---------- GATT Profile Instance ----------
struct gatts_profile_inst {
    esp_gatts_cb_t gatts_cb;
    uint16_t gatts_if;
    uint16_t app_id;
    uint16_t conn_id;
    uint16_t service_handle;
    esp_gatt_srvc_id_t service_id;
    uint16_t char_handle;
    esp_bt_uuid_t char_uuid;
    esp_gatt_perm_t perm;
    esp_gatt_char_prop_t property;
    uint16_t descr_handle;
    esp_bt_uuid_t descr_uuid;
};

#define PROFILE_NUM 2
#define MAX_CONN    PROFILE_NUM

#define PREPARE_BUF_MAX_SIZE        1024
#define BLE_CACHE_MAX_LENGTH        PREPARE_BUF_MAX_SIZE

typedef struct {
    uint8_t *prepare_buf;
    int     prepare_len;
    int     offset;
    int     rest_len;
} prepare_type_env_t;

// static uint8_t ble_write_cache[PROFILE_NUM][BLE_CACHE_MAX_LENGTH];
static uint8_t ble_read_cache[PROFILE_NUM][BLE_CACHE_MAX_LENGTH];

// static prepare_type_env_t prepare_write_env[PROFILE_NUM] = {
//     {
//         .prepare_buf = ble_write_cache[0],
//         .prepare_len = 0,
//         .offset = 0,
//         .rest_len = 0
//     },
//     {
//         .prepare_buf = ble_write_cache[1],
//         .prepare_len = 0,
//         .offset = 0,
//         .rest_len = 0
//     },
// };
static prepare_type_env_t prepare_read_env[PROFILE_NUM] = {
    {
        .prepare_buf = ble_read_cache[0],
        .prepare_len = 0,
        .offset = 0,
        .rest_len = 0
    },
    {
        .prepare_buf = ble_read_cache[1],
        .prepare_len = 0,
        .offset = 0,
        .rest_len = 0
    },
};

typedef enum 
{
    DEVICE_TYPE_UNKNOWN = 0x00,
    DEVICE_TYPE_PHONE = 0x01,
    DEVICE_TYPE_CHARGING_BASE = 0x02
} device_type_e;

typedef struct 
{
    bool connected;
    int16_t conn_id;
    device_type_e device_type;
    // 其他连接相关信息...
} connection_status_t;

static uint16_t ble_mtu_size[PROFILE_NUM] = { 23, 23 };

static gatt_write_call      ps_write_call[PROFILE_NUM]  = {NULL};
static gatt_connect_call    ps_connect_call[PROFILE_NUM]  = {NULL};

static struct gatts_profile_inst heart_rate_profile_tab[HEART_PROFILE_NUM] = {
    [HEART_PROFILE_APP_IDX] = {
        .gatts_cb = NULL, // Callback function
        .gatts_if = ESP_GATT_IF_NONE,
    },
};

static bool    g_notify_enabled[MAX_CONN] = {false, false};

static connection_status_t connect[MAX_CONN] = 
{
    {false, -1, DEVICE_TYPE_UNKNOWN},
    {false, -1, DEVICE_TYPE_UNKNOWN}
};

static TimerHandle_t ble_adv_timer_handle = NULL;

// Find connection index based on conn_id
static int find_conn_index(uint16_t conn_id)
{
    for (int i = 0; i < MAX_CONN; i++) 
    {
        if (connect[i].conn_id == conn_id)
         {
            return i;
        }
    }
    return -1; // Not found
}

// Find a free slot for a new connection
static int find_free_slot(void)
{
    for (int i = 0; i < MAX_CONN; i++) 
    {
        if (connect[i].conn_id < 0)
         {
            return i;
        }
    }
    return -1; // No free slot
}

static uint8_t connected_device_num(void)
{
    uint8_t i = 0;
    uint8_t j = 0;
    for (i = 0; i < MAX_CONN; i++) 
    {
        if (connect[i].connected == 1)
        {
            j++;
        }
    }
    return j;
}

// logical id to conn_id
static uint8_t connid_to_logical(uint8_t id)
{
    uint8_t dest_id = 0;

    if (id >= MAX_CONN)
    {
        return 0;
    }

    if ( connect[id].device_type == DEVICE_TYPE_PHONE)
    {
        dest_id = 0;
    }
    else if (connect[id].device_type == DEVICE_TYPE_CHARGING_BASE)
    {
        dest_id = 1;
    }

    return dest_id;
}

// conn_id to logical id
static uint8_t logical_to_connid(uint8_t id)
{
    uint8_t dest_id = 0;

    switch(id)
    {
        case 0: // object is phone
            if (connect[id].device_type == DEVICE_TYPE_CHARGING_BASE)
            {
                dest_id = 1;
            }
           break;
        case 1: // object is charging base
            if (connect[id].device_type == DEVICE_TYPE_PHONE)
            {
                dest_id = 0;
            }
           break;
        default:
           break;
    }

    return dest_id;
}

static void ble_adv_timer_callback(TimerHandle_t xTimer)
{
    if ((connected_device_num() > 0) && (find_free_slot() >= 0)) 
    {
        esp_err_t err = esp_ble_gap_ext_adv_start(NUM_EXT_ADV_SET, ext_adv);
        if (err != ESP_OK) 
        {
            ESP_LOGW(GATTS_TABLE_TAG, "re-start adv fail, err=0x%x", err);
        } 
        else 
        {
            ESP_LOGI(GATTS_TABLE_TAG, "re-start adv OK => waiting for next device");
        }
    }
}

void gatt_server_ble_adv_start(void)
 {
     static uint8_t flag_ble_timer = 0;
     if (flag_ble_timer == 1)
     {
        xTimerStart(ble_adv_timer_handle, 0);
        return;
     }
     ble_adv_timer_handle = xTimerCreate("ble_adv_timer", 
                                           100 / portTICK_PERIOD_MS, 
                                           pdFALSE,
                                          (void *)1, 
                                          ble_adv_timer_callback);
     flag_ble_timer = 1;
     xTimerStart(ble_adv_timer_handle, 0);
 }

static uint16_t esp_ble_payload_size(uint8_t id)
{
    if (id >= PROFILE_NUM)
    {
        return 0;
    }
    return (ble_mtu_size[id] - 3);
}

// ---------- GAP Event Handler ----------
static void gap_event_handler(esp_gap_ble_cb_event_t event, esp_ble_gap_cb_param_t *param)
{
    switch (event) {
    case ESP_GAP_BLE_EXT_ADV_SET_PARAMS_COMPLETE_EVT:
        ESP_LOGI(GATTS_TABLE_TAG, "Set ext adv params, status %d",
                 param->ext_adv_set_params.status);
        break;
    case ESP_GAP_BLE_EXT_ADV_DATA_SET_COMPLETE_EVT:
        ESP_LOGI(GATTS_TABLE_TAG, "Set ext adv data, status %d",
                 param->ext_adv_data_set.status);
        if(adv_config_done & ADV_CONFIG_FLAG)
        {
            adv_config_done &= ~ADV_CONFIG_FLAG;
            esp_ble_gap_ext_adv_start(NUM_EXT_ADV_SET, ext_adv);
        }
        break;
    case ESP_GAP_BLE_SCAN_RSP_DATA_SET_COMPLETE_EVT:
        ESP_LOGI(GATTS_TABLE_TAG, "Set scan rsp data, status %d",
            param->ext_adv_data_set.status);
        if(adv_config_done & SCAN_RSP_CONFIG_FLAG)
        {
            adv_config_done &= ~SCAN_RSP_CONFIG_FLAG;
            esp_ble_gap_ext_adv_start(NUM_EXT_ADV_SET, ext_adv);
        }
        break;
    case ESP_GAP_BLE_EXT_ADV_START_COMPLETE_EVT:
        ESP_LOGI(GATTS_TABLE_TAG, "Start ext adv complete: status=%d, handle=%d",
            param->ext_adv_start.status, EXT_ADV_HANDLE_1);
        break;
    case ESP_GAP_BLE_ADV_TERMINATED_EVT:
        ESP_LOGI(GATTS_TABLE_TAG, "Ext adv terminated: status=%d, completed_event=%d",
            param->adv_terminate.status, 
            param->adv_terminate.completed_event);

            // 只有在非连接创建的情况下才重启广播
            // 添加延迟避免立即重启
            vTaskDelay(pdMS_TO_TICKS(100));
            if(adv_config_done & ADV_CONFIG_FLAG)
            {
                adv_config_done &= ~ADV_CONFIG_FLAG;
                esp_ble_gap_ext_adv_start(NUM_EXT_ADV_SET, ext_adv);
            }
        break;
    case ESP_GAP_BLE_SET_LOCAL_PRIVACY_COMPLETE_EVT:
        ESP_LOGI(GATTS_TABLE_TAG, "Local privacy done, status %x",
                 param->local_privacy_cmpl.status);
        break;
    case ESP_GAP_BLE_UPDATE_CONN_PARAMS_EVT:
        ESP_LOGI(GATTS_TABLE_TAG,
                 "Conn param update => status %d, conn_int %d, latency %d, timeout %d",
                 param->update_conn_params.status,
                 param->update_conn_params.conn_int,
                 param->update_conn_params.latency,
                 param->update_conn_params.timeout);
        break;
    default:
        break;
    }
}

// ---------- GATTS Event Handler ----------
static void gatts_profile_event_handler(esp_gatts_cb_event_t event,
                                        esp_gatt_if_t gatts_if,
                                        esp_ble_gatts_cb_param_t *param)
{
    uint16_t cid = 0;
    switch (event) {
    case ESP_GATTS_REG_EVT:
        ESP_LOGI(GATTS_TABLE_TAG, "REG_EVT => status %d, app_id %d",param->reg.status, param->reg.app_id);

        esp_err_t set_dev_name_ret = esp_ble_gap_set_device_name((const char *)&ext_adv_raw_data_1[LOCAL_NAME_POSE]);
        if (set_dev_name_ret){
            ESP_LOGE(GATTS_TABLE_TAG, "set device name failed, error code = %x", set_dev_name_ret);
        }
        // Enable local privacy
        esp_ble_gap_config_local_privacy(true);

        // Create GATT attribute table
        esp_ble_gatts_create_attr_tab(gatt_db, gatts_if, HRS_IDX_NB, SVC_INST_ID);
        break;

    case ESP_GATTS_CREAT_ATTR_TAB_EVT: {
        if (param->add_attr_tab.status == ESP_GATT_OK) {
            if (param->add_attr_tab.num_handle == HRS_IDX_NB) {
                ESP_LOGI(GATTS_TABLE_TAG, "Create attr table OK, handle num=%d",
                         param->add_attr_tab.num_handle);
                // Copy handles to profile_handle_table
                memcpy(profile_handle_table, param->add_attr_tab.handles, sizeof(profile_handle_table));
                // Start the service
                esp_ble_gatts_start_service(profile_handle_table[IDX_SVC]);

                // Set extended advertising parameters
                ESP_ERROR_CHECK(esp_ble_gap_ext_adv_set_params(EXT_ADV_HANDLE_1, &ext_adv_params_2M_1));
                ESP_ERROR_CHECK(esp_ble_gap_config_ext_adv_data_raw(EXT_ADV_HANDLE_1,
                                (LOCAL_NAME_POSE + LOCAL_NAME_LENGTH), ext_adv_raw_data_1));
                adv_config_done |= ADV_CONFIG_FLAG;
                // add scan response
                ESP_ERROR_CHECK(esp_ble_gap_config_ext_scan_rsp_data_raw(EXT_ADV_HANDLE_1, 
                                                                        sizeof(scan_rsp_data), 
                                                                        scan_rsp_data));
                adv_config_done |= SCAN_RSP_CONFIG_FLAG;
                ESP_ERROR_CHECK(esp_ble_gap_ext_adv_start(NUM_EXT_ADV_SET, ext_adv));
            } else {
                ESP_LOGE(GATTS_TABLE_TAG,
                         "Create table mismatch => handle=%d != HRS_IDX_NB(%d)",
                         param->add_attr_tab.num_handle, HRS_IDX_NB);
            }
        } else {
            ESP_LOGE(GATTS_TABLE_TAG, "Create attr table failed, error=0x%x",
                     param->create.status);
        }
        break;
    }

    case ESP_GATTS_READ_EVT:
        // ESP_LOGI(GATTS_TABLE_TAG, "READ_EVT => handle=%d", param->read.handle);
        esp_gatt_rsp_t rsp;
        memset(&rsp, 0, sizeof(esp_gatt_rsp_t));
        
        cid = param->read.conn_id;
        ESP_LOGI(GATTS_TABLE_TAG, "read_conn_id = %d read_handle = %d \r\n" ,param->read.conn_id ,param->read.handle);
        cid = connid_to_logical(cid);
        if ((prepare_read_env[cid].prepare_buf)
        && (prepare_read_env[cid].rest_len > 0))
        {
            // single response is limited to (mtu_size - 3) bytes
            int32_t curr_send = prepare_read_env[cid].rest_len > esp_ble_payload_size(cid) ? esp_ble_payload_size(cid) : prepare_read_env[cid].rest_len;

            rsp.attr_value.handle   = param->read.handle;
            rsp.attr_value.offset   = prepare_read_env[cid].offset;
            rsp.attr_value.len      = prepare_read_env[cid].rest_len;

            uint32_t base = prepare_read_env[cid].offset;
            if (base == 0)
            {
                int32_t copy_first = curr_send;
                memcpy(rsp.attr_value.value,  prepare_read_env[cid].prepare_buf, copy_first);
            }
            else
            {
                memcpy(rsp.attr_value.value,  prepare_read_env[cid].prepare_buf + base, curr_send);
            }

            esp_ble_gatts_send_response(gatts_if, param->read.conn_id, param->read.trans_id,
                                        ESP_GATT_OK, &rsp);

            prepare_read_env[cid].offset     += curr_send;
            prepare_read_env[cid].rest_len   -= curr_send;
            if (prepare_read_env[cid].rest_len <= 0)
            {
                prepare_read_env[cid].rest_len = 0;
            }
        }
        else
        {
            rsp.attr_value.handle = param->read.handle;
            rsp.attr_value.len = 1;
            rsp.attr_value.value[0] = connect[param->read.conn_id].device_type;
            esp_ble_gatts_send_response(gatts_if, param->read.conn_id, param->read.trans_id,
                                        ESP_GATT_OK, &rsp);
        }
        break;

    case ESP_GATTS_WRITE_EVT: {
        cid = param->write.conn_id;
        ESP_LOGI(GATTS_TABLE_TAG, "WRITE_EVT => conn_id=%d, handle=%d", cid, param->write.handle);
        ESP_LOG_BUFFER_HEX(GATTS_TABLE_TAG, param->write.value, param->write.len);

        if (!param->write.is_prep) {

            /* send response when param->write.need_rsp is true*/
            if (param->write.need_rsp)
            {
                esp_ble_gatts_send_response(gatts_if, param->write.conn_id, param->write.trans_id, ESP_GATT_OK, NULL);
            }

            cid = connid_to_logical(cid);
            if (ps_write_call[cid])
            {
                ps_write_call[cid](cid, param->write.value, param->write.len);
            }

            // Handle CCC (Client Characteristic Configuration)
            if (param->write.handle == profile_handle_table[IDX_CHAR_CFG_A]) {
                if (param->write.len == 2) {
                    uint16_t ccc_val = (param->write.value[1] << 8) | param->write.value[0];
                    if ((ccc_val == DEVICE_TYPE_PHONE) || (ccc_val == DEVICE_TYPE_CHARGING_BASE)) {
                        int idx = find_conn_index(cid);
                        if (idx >= 0) {
                            // g_notify_enabled[idx] = enabled;
                            connect[idx].device_type = ccc_val;
                            ESP_LOGI(GATTS_TABLE_TAG, "conn_id=%d => device_type=%d", idx, connect[idx].device_type);
                        }
                    } else {
                        ESP_LOGI(GATTS_TABLE_TAG, "unknow device_type!!!\r\n");
                        ESP_LOGI(GATTS_TABLE_TAG, "conn_id=%d => device_type=%d", param->write.conn_id, ccc_val);
                    }
                    
                }
            }
        }
        break;
    }

    case ESP_GATTS_MTU_EVT:
        cid = param->mtu.conn_id;
        ble_mtu_size[cid] = param->mtu.mtu;
        esp_ble_gatt_set_local_mtu(param->mtu.mtu);
        ESP_LOGI(GATTS_TABLE_TAG, "MTU exchange, conn_id=%d => MTU=%d", cid, param->mtu.mtu);
        break;

    case ESP_GATTS_CONNECT_EVT: {
        cid = param->connect.conn_id;
        ESP_LOGI(GATTS_TABLE_TAG, "CONNECT_EVT => conn_id=%d, remote="ESP_BD_ADDR_STR,
                 cid, ESP_BD_ADDR_HEX(param->connect.remote_bda));

        // Find a free slot for the new connection
        int slot = find_free_slot();
        if (slot >= 0) {
            g_notify_enabled[slot] = false;  // Notification disabled by default
            ESP_LOGI(GATTS_TABLE_TAG, ">>> new device => slot=%d, conn_id=%d", slot, cid);
            connect[slot].conn_id = cid;
            connect[slot].connected = true;
            if (ps_connect_call[slot])
            {
                ps_connect_call[slot](slot, 1);
            }
            // Restart advertising if there are free slots
            if (find_free_slot() >= 0) {
                // esp_err_t err = esp_ble_gap_ext_adv_start(NUM_EXT_ADV_SET, ext_adv);
                // if (err != ESP_OK) {
                //     ESP_LOGW(GATTS_TABLE_TAG, "re-start adv fail, err=0x%x", err);
                // } else {
                //     ESP_LOGI(GATTS_TABLE_TAG, "re-start adv OK => waiting for next device");
                // }
            } else {
                ESP_LOGI(GATTS_TABLE_TAG, "No more slots => not re-starting adv");
            }
        } else {
            ESP_LOGW(GATTS_TABLE_TAG, "No free slot => conn_id=%d", cid);
        }
        break;
    }

    case ESP_GATTS_DISCONNECT_EVT: {
        uint16_t d_cid = param->disconnect.conn_id;
        ESP_LOGI(GATTS_TABLE_TAG, "DISCONNECT_EVT => conn_id=%d, reason=0x%x",
                 d_cid, param->disconnect.reason);

        int idx = find_conn_index(d_cid);
        if (idx >= 0) {
            g_notify_enabled[idx] = false;
            ESP_LOGI(GATTS_TABLE_TAG, "Device disconnected => slot=%d, conn_id=%d", idx, d_cid);
            idx = connid_to_logical(idx);
            connect[idx].conn_id = -1;
            connect[idx].connected = false;
            connect[idx].device_type = DEVICE_TYPE_UNKNOWN;
            if (ps_connect_call[idx])
            {
                ps_connect_call[idx](idx, 0);
            }
        }

        esp_err_t err = esp_ble_gap_ext_adv_start(NUM_EXT_ADV_SET, ext_adv);
        if (err != ESP_OK) 
        {
            ESP_LOGW(GATTS_TABLE_TAG, "re-start adv after disconnect fail, err=0x%x", err);
        } else 
        {
            ESP_LOGI(GATTS_TABLE_TAG, "re-start adv after disconnect => ok");
        }
        break;
    }

    default:
        break;
    }
}

// ---------- GATTS Profile Event Handler ----------
static void gatts_event_handler(esp_gatts_cb_event_t event,
                                esp_gatt_if_t gatts_if,
                                esp_ble_gatts_cb_param_t *param)
{
    if (event == ESP_GATTS_REG_EVT) {
        // Register the GATT profile
        if (param->reg.status == ESP_GATT_OK) {
            heart_rate_profile_tab[HEART_PROFILE_APP_IDX].gatts_if = gatts_if;
            heart_rate_profile_tab[HEART_PROFILE_APP_IDX].gatts_cb = gatts_profile_event_handler;
        } else {
            ESP_LOGI(GATTS_TABLE_TAG, "REG_EVT fail => app_id=%04x, status=%d",
                     param->reg.app_id, param->reg.status);
            return;
        }
    }

    for (int idx = 0; idx < HEART_PROFILE_NUM; idx++) {
        if (gatts_if == ESP_GATT_IF_NONE ||
            gatts_if == heart_rate_profile_tab[idx].gatts_if) {
            if (heart_rate_profile_tab[idx].gatts_cb) {
                heart_rate_profile_tab[idx].gatts_cb(event, gatts_if, param);
            }
        }
    }
}

static void get_music_station_name(uint8_t *chip_id, uint8_t len)
{
    if (len == 6)
    {   
        sprintf((char *)&ext_adv_raw_data_1[LOCAL_NAME_POSE], "A01-%02X%02X%02X%02X%02X%02X",
                                     chip_id[0], chip_id[1], chip_id[2], 
                                     chip_id[3], chip_id[4], chip_id[5]);
        ext_adv_raw_data_1[LOCAL_NAME_POSE - 2] = 12 + 4 + 1;
                                            
    } 
    else
    {
        ESP_LOGE(GATTS_TABLE_TAG, "Invalid chip_id length: %d, expected: 6", len);
    }
}

// ---------- GATT Server Initialization ----------
int32_t gatt_server_init(uint8_t *chip_id, uint8_t len)
{
    get_music_station_name(chip_id, len);

    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    ESP_ERROR_CHECK(esp_bt_controller_mem_release(ESP_BT_MODE_CLASSIC_BT));

    esp_bt_controller_config_t bt_cfg = BT_CONTROLLER_INIT_CONFIG_DEFAULT();
    ret = esp_bt_controller_init(&bt_cfg);
    if (ret) {
        ESP_LOGE(GATTS_TABLE_TAG, "bt_controller_init fail => %s", esp_err_to_name(ret));
        return -1;
    }
    ret = esp_bt_controller_enable(ESP_BT_MODE_BLE);
    if (ret) {
        ESP_LOGE(GATTS_TABLE_TAG, "bt_controller_enable fail => %s", esp_err_to_name(ret));
        return -1;
    }
    ESP_LOGI(GATTS_TABLE_TAG, "Bluetooth init OK");

    ret = esp_bluedroid_init();
    if (ret) {
        ESP_LOGE(GATTS_TABLE_TAG, "bluedroid_init fail => %s", esp_err_to_name(ret));
        return -1;
    }
    ret = esp_bluedroid_enable();
    if (ret) {
        ESP_LOGE(GATTS_TABLE_TAG, "bluedroid_enable fail => %s", esp_err_to_name(ret));
        return -1;
    }

    // Register GATTS and GAP callbacks
    ret = esp_ble_gatts_register_callback(gatts_event_handler);
    if (ret) {
        ESP_LOGE(GATTS_TABLE_TAG, "gatts register err=0x%x", ret);
        return -1;
    }
    ret = esp_ble_gap_register_callback(gap_event_handler);
    if (ret) {
        ESP_LOGE(GATTS_TABLE_TAG, "gap register err=0x%x", ret);
        return -1;
    }
    // Register GATT application
    ret = esp_ble_gatts_app_register(ESP_HEART_RATE_APP_ID);
    if (ret) {
        ESP_LOGE(GATTS_TABLE_TAG, "gatts app reg err=0x%x", ret);
        return -1;
    }

    esp_err_t local_mtu_ret = esp_ble_gatt_set_local_mtu(ble_mtu_size[0]);
    if (local_mtu_ret){
        ESP_LOGE(GATTS_TABLE_TAG, "set local MTU failed, error code = %x", local_mtu_ret);
        return -1;
    }

    return 0;
}

void gatt_server_notify(uint8_t id, uint8_t *data, uint32_t len)
{
    uint16_t i = 0;
    uint16_t payload_len = esp_ble_payload_size(id);
    uint8_t pack_num    = len / payload_len;
    uint8_t is_not_full = len % payload_len;
    
    if (id >= MAX_CONN)
    {
        return;
    }

    id = logical_to_connid(id);
    if (connect[id].connected)
    {
        // Send notifications in packets
        for (i = 0; i < pack_num; i++)
        {
            esp_ble_gatts_send_indicate(heart_rate_profile_tab[0].gatts_if,
                                    id,
                                    profile_handle_table[IDX_CHAR_VAL_A],
                                    payload_len, &data[payload_len * i], false);
        }
        // Send remaining data if not full
        if (is_not_full)
        {
            uint16_t remain_len = len - payload_len * i;
            esp_ble_gatts_send_indicate(heart_rate_profile_tab[0].gatts_if,
                                    id,
                                    profile_handle_table[IDX_CHAR_VAL_A],
                                    remain_len, &data[payload_len * i], false);
            i = 0;
            is_not_full = 0;
        }
    }
}

void gatt_server_regist_write_call(uint8_t id, gatt_write_call p)
{
    if (id < PROFILE_NUM)
    {
        ps_write_call[id] = p;
    }
}

void gatt_server_regist_connect_call(uint8_t id, gatt_connect_call p)
{
    if (id < PROFILE_NUM)
    {
        ps_connect_call[id] = p;
    }
}

void gatt_server_prepare_read(uint8_t id, uint8_t *data, uint32_t len)
{
    if (id >= PROFILE_NUM)
    {
        return;
    }
    memcpy(prepare_read_env[id].prepare_buf, data, len);
    prepare_read_env[id].prepare_len   = len;
    prepare_read_env[id].offset        = 0;
    prepare_read_env[id].rest_len      = len;
}
