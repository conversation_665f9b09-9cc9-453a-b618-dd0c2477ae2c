#include "hash_search.h"
#include "logger.h"

#define tag "[HashSearch]"

int32_t hash_search_add(hash_search_str_t **elem, const char* name, int32_t index)
{
    hash_search_str_t* temp = (hash_search_str_t*)malloc(sizeof(hash_search_str_t));
    if (temp == NULL)
    {
        mlog_int_e(tag, 2000, "malloc error");
        return -1;
    }
    strcpy(temp->name, name);
    temp->index = index;
    HASH_ADD_STR(*elem, name, temp);
    return 0;
}

int32_t hash_search_check(hash_search_str_t **elem, const char* name)
{
    hash_search_str_t*  temp = NULL;
    const char* find_str = name;
    HASH_FIND_STR(*elem, find_str, temp);
    if (temp == NULL)
    {
        return -1;
    }
    return  temp->index;
}

