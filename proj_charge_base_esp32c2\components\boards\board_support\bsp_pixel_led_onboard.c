#include <stdlib.h>
#include <string.h>
#include <sys/cdefs.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_err.h"
#include "esp_log.h"
#include "esp_attr.h"
#include "esp_check.h"
#include "driver/rmt_tx.h"
#include "logger.h"
#include "../board_define.h"
#include "esp_system.h"
#include "driver/spi_master.h"
#include "driver/gpio.h"


#define PIN_NUM_MOSI 18

#define 	PIXEL_NUM	(1)

#define 	BIT_1 		248
#define 	BIT_0 		224

#define 	FRAME_LEN	(4 + 24 * PIXEL_NUM)

uint8_t 	frames[FRAME_LEN];
uint8_t 	rbufer[PIXEL_NUM] = {0};
uint8_t 	gbufer[PIXEL_NUM] = {0};
uint8_t 	bbufer[PIXEL_NUM] = {0};

static spi_device_handle_t spi;
int32_t bsp_pixel_led_onboard_init()
{
    esp_err_t ret;
    spi_bus_config_t buscfg = {
        .mosi_io_num = PIN_NUM_MOSI,
        .miso_io_num = -1,
        .sclk_io_num = -1, 
        .quadwp_io_num = -1,
        .quadhd_io_num = -1,
        .max_transfer_sz = 20000,
    };

    spi_device_interface_config_t devcfg = {
        .clock_speed_hz = 8000000,
        .mode = 0,             
        .spics_io_num = -1,       
        .queue_size = 1,
    };
    ret = spi_bus_initialize(SPI2_HOST, &buscfg, SPI_DMA_CH_AUTO);
    if (ret != ESP_OK) {
        return -1;
    }
    ret = spi_bus_add_device(SPI2_HOST, &devcfg, &spi);
    if (ret != ESP_OK) {
        return -1;
    }
    return 0;
}
int32_t bsp_pixel_led_send_data(const uint8_t *data, int len)
{
    esp_err_t ret;
    spi_transaction_t t;
    if (len==0) return -1;             //no need to send anything
    memset(&t, 0, sizeof(t));       //Zero out the transaction
    t.length=len*8;                 //Len is in bytes, transaction length is in bits.
    t.tx_buffer=data;               //Data
    ret=spi_device_polling_transmit(spi, &t);  
    return (ret == ESP_OK) ? 0 : -1;
}
static void buffer_update(uint8_t* rbuf, uint8_t* gbuf, uint8_t* bbuf)
{			 
	uint32_t i = 0, j = 0;
	uint32_t id = 0;
	memset(frames, 0, sizeof(frames));

	for(i = 0; i < PIXEL_NUM; i++)
	{
		id = PIXEL_NUM - i - 1;			
		for(j = 0; j < 8; j++)
		{
			frames[3 + 24 * id + j]			= (gbuf[i] & (0x80 >> j)) ? BIT_1 : BIT_0;
			frames[3 + 24 * id + j + 8]		= (rbuf[i] & (0x80 >> j)) ? BIT_1 : BIT_0;
			frames[3 + 24 * id + j + 16]	= (bbuf[i] & (0x80 >> j)) ? BIT_1 : BIT_0;
		}
	}
	frames[0] = 0;
	frames[1] = 0;
	frames[2] = 0;
	frames[4 + 24 * PIXEL_NUM - 1] = 0; 
}
int32_t bsp_pixel_led_onboard_set(uint32_t id, uint8_t r, uint8_t g, uint8_t b)
{
    if (id < PIXEL_NUM)
	{
		rbufer[id] = r;
		gbufer[id] = g;
		bbufer[id] = b;
	}
    return 0;
}

int32_t bsp_pixel_led_onboard_flush()
{
    buffer_update(rbufer, gbufer, bbufer);	
    bsp_pixel_led_send_data(frames,FRAME_LEN);
    return 0;
}

int32_t bsp_pixel_led_onboard_clear()
{
    return 0;
}
