#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_system.h"
#include "esp_event.h"
#include "esp_ota_ops.h"
#include "esp_app_desc.h"
#include "logger.h"
#include "str_function.h"

#define tag     "fw_ota"

static const esp_partition_t *update_partition = NULL;
static esp_ota_handle_t update_handle = 0 ;
static uint32_t wrote_len = 0;

int32_t firmware_ota_start()
{
    update_partition = esp_ota_get_next_update_partition(NULL);
    if (update_partition == NULL)
    {
        mlog_e(tag, "ota partition find failed");
        return -1;
    }
    mlog_i(tag, "Writing to partition subtype %d at offset 0x%"PRIx32,
             update_partition->subtype, update_partition->address);

    esp_err_t err = esp_ota_begin(update_partition, 
                                OTA_WITH_SEQUENTIAL_WRITES, 
                                &update_handle);
    if (err != ESP_OK) 
    {
        mlog_e(tag, "esp_ota_begin failed (%s)", esp_err_to_name(err));       
        esp_ota_abort(update_handle);
        return -1;
    } 
    wrote_len = 0;
    return 0;
}

int32_t firmware_ota_write(uint8_t *data, uint32_t len)
{
    esp_err_t err = esp_ota_write(update_handle, (const void *)data, len);
    if (err != ESP_OK) 
    {        
        esp_ota_abort(update_handle);
        return -1;
    }
    wrote_len += len;
    //mlog_i(tag, "image wrote len %d", wrote_len);       
    return 0;
}

int32_t firmware_ota_ending()
{
    esp_err_t err = esp_ota_end(update_handle);
    if (err != ESP_OK) 
    {
        if (err == ESP_ERR_OTA_VALIDATE_FAILED) 
        {
            mlog_e(tag, "Image validation failed, image is corrupted");
        } 
        else 
        {
            mlog_e(tag, "esp_ota_end failed (%s)!", esp_err_to_name(err));
        }
        return -1;
    }

    err = esp_ota_set_boot_partition(update_partition);
    if (err != ESP_OK) 
    {        
        mlog_e(tag, "esp_ota_set_boot_partition failed (%s)!", esp_err_to_name(err));
        return -1;
    }

    mlog_w(tag, "Prepare to restart system! updated:[%s]",
        update_partition->label);

    return 0;
}

int32_t firmware_ota_get_buildtime(char *data, uint32_t *len)
{
    uint8_t lenin  = *len;
    uint8_t lenout = *len;
    const esp_app_desc_t *app_desc = esp_app_get_description();
    if (app_desc == NULL)
    {
        return -1;
    }
    lenout = snprintf(data, lenin, "%s %s", app_desc->date, app_desc->time);
    *len = lenout;
    //mlog_i(tag, "get firmware buildtime:%s, len:%d", data, *len);
    return 0;
}

int32_t firmware_ota_get_version(uint32_t *version)
{
    const esp_app_desc_t *app_desc = esp_app_get_description();
    if (app_desc == NULL)
    {
        return -1;
    }
    version_string_to_numeric(app_desc->version, version);
    //mlog_i(tag, "get firmware version:0x%x ", version);
    return 0;
}

int32_t firmware_ota_get_proj_name(char *data, uint32_t *len)
{
    uint8_t lenin  = *len;
    uint8_t lenout = *len;
    const esp_app_desc_t *app_desc = esp_app_get_description();
    if (app_desc == NULL)
    {
        return -1;
    }
    lenout = snprintf(data, lenin, "%s", app_desc->project_name);
    *len = lenout;
    //mlog_i(tag, "get project name:%s, len:%d", data, *len);
    return 0;
}