#ifndef _H_HASH_SEARCH_H
#define _H_HASH_SEARCH_H

#include "uthash.h"
#include <stdlib.h>   /* malloc */
#include <stdint.h>

typedef struct
{
	char		name[32];
	int32_t		index;
	UT_hash_handle hh;
} hash_search_str_t;

#ifdef __cplusplus
extern "C" {
#endif

int32_t hash_search_add(hash_search_str_t **elem, const char* name, int32_t index);
int32_t hash_search_check(hash_search_str_t **elem, const char* name);

#ifdef __cplusplus
}
#endif

#endif /* _H_CHANNEL_POOL_H */
