# Target labels
 bootloader
# Source files and their labels
D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/CMakeFiles/bootloader
D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/CMakeFiles/bootloader.rule
D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/CMakeFiles/bootloader-complete.rule
D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule
D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule
D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule
D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule
D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule
D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule
D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule
