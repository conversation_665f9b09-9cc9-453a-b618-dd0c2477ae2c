set(COMPONENT_REQUIRES
    nvs_flash
    esp_adc
    esp_driver_gpio
    esp_driver_i2c
    esp_driver_uart
    esp_timer
    driver
    tools
    devices
    drivers
    protocol
)

list(APPEND COMPONENT_ADD_INCLUDEDIRS 	./
										./board_support
										./peripheral
										)

set(COMPONENT_SRCS
./board_support/bsp_timestamp.c
./board_support/bsp_nvs.c
./board_support/bsp_uart0_commander.c
./board_support/bsp_chip_id.c
./board_support/bsp_gpio_ctrl.c
./board_support/bsp_gpio_detect.c
./board_support/bsp_pixel_led_onboard.c
./board_support/bsp_i2c0_bus.c
./peripheral/i2c_device.c
./peripheral/hardware_link.c
./peripheral/local_device.c
)

register_component()
