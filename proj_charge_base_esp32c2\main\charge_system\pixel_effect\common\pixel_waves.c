#include "pixel_waves.h"
#include "local_device.h"
#include <math.h>
#include <string.h>


int32_t pixwaves_linear_init(wave_linear_t *p, wave_point_t *src, wave_point_t *dst)
{
	memcpy(&p->src, src, sizeof(wave_point_t));
	memcpy(&p->dst, dst, sizeof(wave_point_t));
	p->finished = 0;

	float delt_y = dst->value - src->value;
	float delt_x = dst->t - src->t;

	p->ratio = delt_y / delt_x;
	return 0;
}

float pixwaves_linear_value(wave_linear_t *p)
{
	if (p->finished)
	{
		return p->value;
	}

	if (dev_charge()->time.get_seconds() > p->dst.t)
	{
		p->finished = 1;
		p->value  = p->dst.value;
		return p->value;
	}

	float dt = dev_charge()->time.get_seconds() - p->src.t;
	return p->src.value + (dt * p->ratio);
}
