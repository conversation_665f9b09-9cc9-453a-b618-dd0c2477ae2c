#ifndef _TYPE_NVS_STORAGE_H_
#define _TYPE_NVS_STORAGE_H_

#include <stdint.h>

typedef struct
{
    int32_t (*init)(void);
    int32_t (*set_str)(const char *, const char *);
    int32_t (*get_str)(const char *, char *);
    int32_t (*set_u32)(const char *, uint32_t );
    int32_t (*get_u32)(const char *, uint32_t *);
    int32_t (*set_i8)(const char *, int8_t );
    int32_t (*get_i8)(const char *, int8_t *);
    int32_t (*set_u8)(const char *, uint8_t );
    int32_t (*get_u8)(const char *, uint8_t *);
    int32_t (*set_i16)(const char *, int16_t );
    int32_t (*get_i16)(const char *, int16_t *);
    int32_t (*set_u16)(const char *, uint16_t );
    int32_t (*get_u16)(const char *, uint16_t *);
    int32_t (*set_blob)(const char *, uint8_t *, uint32_t );
    int32_t (*get_blob)(const char *, uint8_t *, uint32_t *);
}dev_nvs_t;

#endif
