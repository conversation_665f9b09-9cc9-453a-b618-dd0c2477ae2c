#ifndef _PIXEL_WAVES_H_
#define _PIXEL_WAVES_H_

#include <stdint.h>

typedef struct
{
 	float 			t;
 	float 			value;
}wave_point_t;

typedef struct
{
	int32_t  		finished;
	float 			timestamp;
	float 			value;
	float 			ratio;

	wave_point_t  	src;
	wave_point_t  	dst;
}wave_linear_t;

#ifdef __cplusplus
 extern "C" {
#endif


int32_t pixwaves_linear_init(wave_linear_t *p, wave_point_t *src, wave_point_t *dst);
float pixwaves_linear_value(wave_linear_t *p);

#ifdef __cplusplus
 }
#endif

#endif
