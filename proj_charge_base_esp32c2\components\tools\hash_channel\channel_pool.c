#include "type_channel_pool.h"
#include "logger.h"

#define tag "[Channel Pool]"

int32_t channel_pool_init(channel_element_t **pool, uint32_t max_channel)
{
    for (uint32_t i = 0; i < max_channel; i++)
    {
        channel_element_t*  temp = (channel_element_t*)malloc(sizeof(channel_element_t));
        if (temp == NULL)
        {
            mlog_e(tag, "malloc error");
            return -1;
        }
        temp->element_id    = IDLE_ELEM_ID;
        temp->channel_id    = i;
        HASH_ADD_INT(*pool, element_id, temp);
    }
    return 0;
}

int32_t channel_pool_check(channel_element_t **pool, int32_t elem_id)
{
    channel_element_t*  temp = NULL;
    int32_t find_i = elem_id;
    HASH_FIND_INT(*pool, &find_i, temp);
    if (temp == NULL)
    {
        return -1;
    }
    return  temp->channel_id;
}

int32_t channel_pool_in(channel_element_t **pool, int32_t elem_id)
{
    // unique check !!!
    int32_t ch_id = channel_pool_check(pool, elem_id);
    if (ch_id >= 0)
    {
        return ch_id;
    }

    channel_element_t *temp;
    int32_t find_i = IDLE_ELEM_ID;
    HASH_FIND_INT(*pool, &find_i, temp);
    if (temp == NULL)
    {
        mlog_d(tag, "no empty channel");
        return -1;
    }

    HASH_DEL(*pool, temp);
    temp->element_id  = elem_id;
    HASH_ADD_INT(*pool, element_id, temp);
    return temp->channel_id;
}

int32_t channel_pool_out(channel_element_t **pool, int32_t elem_id)
{
    channel_element_t*  temp = NULL;
    int32_t find_i = elem_id;
    HASH_FIND_INT(*pool, &find_i, temp);
    if (temp == NULL)
    {
        // not found
        return -1;
    }
    HASH_DEL(*pool, temp);
    temp->element_id  = IDLE_ELEM_ID;
    HASH_ADD_INT(*pool, element_id, temp);
    return temp->channel_id;
}

int32_t channel_pool_all_out(channel_element_t **pool)
{
    channel_element_t *element = NULL;
    for (element = *pool; element != NULL; element = (channel_element_t *)element->hh.next)
    {
        HASH_DEL(*pool, element);
        element->element_id  = IDLE_ELEM_ID;
        HASH_ADD_INT(*pool, element_id, element);
    }
    return 0;
}

int32_t channel_pool_get_num(channel_element_t **pool)
{
    int32_t active_pool_num = 0;
    channel_element_t *element = NULL;
    for (element = *pool; (element != NULL) && (element->element_id != IDLE_ELEM_ID);)
    {
        active_pool_num ++;
        element = (channel_element_t *)element->hh.next;
    }
    return active_pool_num;
}

int32_t channel_pool_dump(channel_element_t **pool, int32_t *online, int32_t *num)
{
    if (*pool == NULL || online == NULL || pool == NULL)
    {
        mlog_e(tag, "ptr null");
        return -1;
    }

    int32_t max_dump = *num;
    uint32_t active_nums = 0;
    channel_element_t *element = NULL;
    for (element = *pool; element != NULL; element = (channel_element_t *)element->hh.next)
    {
        if ((element->element_id != IDLE_ELEM_ID))
        {
            if (active_nums < max_dump)
            {
                online[active_nums]  = element->channel_id;
            }
            else
            {
                mlog_w(tag, "max dump num:%d < pool num:%d", max_dump, active_nums);
            }
            active_nums ++;
        }
    }
    *num = active_nums;
    return 0;
}

