#ifndef _H_PIXELEFF_SYSTEM_H_
#define _H_PIXELEFF_SYSTEM_H_

#include <stdint.h>
#include "pixel_common.h"
typedef enum
{
	EFF_SYS_IDLE = 0,
	EFF_SYS_CHARGE_DETECT,
	EFF_SYS_CHARGE_SLOW,
	EFF_SYS_CHARGE_FAST,
	EFF_SYS_CHARGE_FULL,
	EFF_SYS_USB_BUS,
}state_effect_system_e;
 
#ifdef __cplusplus
extern "C" {
#endif

int32_t pixeleff_system_init(void);
void pixeleff_system_update(void);
pixels_system_t* pixs_system();
int32_t pixeleff_system_breathe(float time,colour_t colour);
int32_t pixeleff_system_flicker(float time,float buty,colour_t colour);
int32_t pixeleff_system_colour(colour_t colour,float value);
int32_t pixeleff_system_breathe_color(float time);
#ifdef __cplusplus
}
#endif

#endif
