#ifndef _H_TYPE_DETECT_H
#define _H_TYPE_DETECT_H

#include <stdint.h>

typedef void (*pfunc_interrupt_call)(void);

typedef struct
{
    int32_t (*init)(void);
    int32_t (*get_state)(void);     // return 0/1/-1
}dev_gpio_detect_t;

typedef struct
{
    int32_t (*init)(void);
    int32_t (*get_state)(void);     // return 0/1/-1
    void (*regist_call)(pfunc_interrupt_call);
}dev_gpio_interrupt_t;

typedef struct
{
    int32_t (*init)(void);
    int32_t (*get_state)(uint8_t);     // return 0/1/-1
    void (*regist_call)(uint8_t, pfunc_interrupt_call);
}dev_gpio_inter_multi_t;

#endif
