#ifndef _H_STATE_SYSTEM_H_
#define _H_STATE_SYSTEM_H_

#include <stdint.h>
#include "pixel_common.h"
#ifdef __cplusplus
extern "C" {
#endif
void    system_update(void);
int32_t system_colour(colour_t colour);
int32_t system_breathe(float time);
int32_t system_breathe_color(float time);
int32_t system_flicker(float time,float buty);
void    system_fixed_value(float value);
#ifdef __cplusplus
}
#endif

#endif
