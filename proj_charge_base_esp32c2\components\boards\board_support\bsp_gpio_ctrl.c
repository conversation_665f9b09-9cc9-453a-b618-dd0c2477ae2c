#include "driver/gpio.h"
#include "esp_system.h"
#include "logger.h"

#define GPIO_DC_POWER_EN        (7)

int32_t bsp_gpio_out_power_on(void)
{
    esp_err_t ret = gpio_set_level(GPIO_DC_POWER_EN, 1);
    return (ret == ESP_OK) ? 0: -1;
}

int32_t bsp_gpio_out_power_off(void)
{
    esp_err_t ret = gpio_set_level(GPIO_DC_POWER_EN, 0);
    return (ret == ESP_OK) ? 0: -1;
}

int32_t bsp_gpio_out_power_init(void)
{
    gpio_config_t io_conf;
    io_conf.intr_type = GPIO_INTR_DISABLE;
    io_conf.mode = GPIO_MODE_OUTPUT;
    io_conf.pin_bit_mask = (1ULL << GPIO_DC_POWER_EN);
    io_conf.pull_down_en = 0;
    io_conf.pull_up_en = 1;
    gpio_config(&io_conf);
    bsp_gpio_out_power_on();
    return 0;
}
