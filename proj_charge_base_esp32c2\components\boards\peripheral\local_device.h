#ifndef __LOCAL_DEVICE_H__
#define __LOCAL_DEVICE_H__

#include "stdint.h"
#include "sdkconfig.h"

#include "type_timestamp.h"
#include "type_nvs_storage.h"
#include "type_comport.h"
#include "type_pixel_led.h"
#include "type_chip.h"
#include "type_gpio_ctrl.h"
#include "type_adc.h"


typedef struct
{  
 	dev_timestamp_t 		time;
 	dev_nvs_t 				nvs;
 	dev_chip_id_t  			chip_id;
	dev_gpio_ctrl_t			gpio_power_ctrl;
	dev_pixel_led_t			pixel_onboard;
 	dev_i2c_bus_t 			i2c0_bus;
 	dev_comport_t			comport0;
	dev_adc_t               vbus_volt;

}device_t;

#ifdef __cplusplus
extern "C" {
#endif
 
int32_t board_device_init(void);
 
const device_t* dev_charge(void);

#ifdef __cplusplus
}
#endif

#endif

