# ninja log v6
57	230	7754860742191453	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/esp-idf/esp_system/ld/sections.ld.in	2d6dd77abefc5dcd
45	223	7754860742121533	esp-idf/esp_system/ld/memory.ld	8a25d0a0101ffaa1
8283	8613	7754860822787498	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/bignum_mod.c.obj	c8334dfad6356b17
75	137	7754860741244314	project_elf_src_esp32c2.c	8f1f333692146813
4293	4650	7754860782893476	esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_encrypted_partition.cpp.obj	925a4079ea0fa3d2
8972	9338	7754860829682252	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_ecp.c.obj	9e91af6befe29cdd
75	137	7754860741244314	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/project_elf_src_esp32c2.c	8f1f333692146813
14022	14354	7754860880188343	esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/proto-c/sec2.pb-c.c.obj	f073758fecd803f1
86	167	7754860741507621	ota_data_initial.bin	e1b2afcae1426a1a
5623	5958	7754860796189512	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c2/chip_info.c.obj	2beabce3f9d63a88
13357	13671	7754860873531705	esp-idf/riscv/CMakeFiles/__idf_riscv.dir/interrupt.c.obj	254685bd8b514b11
670	1011	7754860746658702	esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/esp_tls_platform_port.c.obj	4e419a1061c4f01d
13502	13823	7754860874980382	esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_dstr.c.obj	2838a1acd8b68c19
11642	11770	7754860856380384	esp-idf/bootloader_support/libbootloader_support.a	303cf3832ff4f1b5
394	727	7754860743897094	esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/transport.c.obj	c029fd06a242424c
4687	5026	7754860786825254	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/poll.c.obj	527a8c4ec9504a68
86	167	7754860741507621	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/ota_data_initial.bin	e1b2afcae1426a1a
6174	6489	7754860801704514	esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_flash_encrypt_hal_iram.c.obj	8d27d3fbb74cd27e
6905	7233	7754860809004295	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/brownout.c.obj	59db415f49b95bf8
7534	7876	7754860815303220	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c2/secure_boot_secure_features.c.obj	43a56d3e1e81d2be
3922	4265	7754860779181640	esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/src/sdspi_transaction.c.obj	2c97a35d6536b976
6343	6663	7754860803385714	esp-idf/hal/CMakeFiles/__idf_hal.dir/adc_oneshot_hal.c.obj	1f07c8ea32975429
14678	14993	7754860886741011	esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_common.c.obj	635e698054316342
9537	9647	7754860835329098	esp-idf/esp_ringbuf/libesp_ringbuf.a	c0f22027f058dbce
3432	3757	7754860774280280	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/hooks/lwip_default_hooks.c.obj	b6cb69ad5210d1ed
433	768	7754860744293824	esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/transport_socks_proxy.c.obj	cc98d5243e8444a0
312	641	7754860743076735	esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/httpd_ws.c.obj	84e5d6498e20f2c2
5309	5635	7754860793043125	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_event.c.obj	3cb67f8979762172
6319	6640	7754860803144576	esp-idf/hal/CMakeFiles/__idf_hal.dir/gdma_hal_ahb_v1.c.obj	ee34458cb2d8dddb
9182	9535	7754860831778803	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/sha256.c.obj	d8f8bf3593a70ea9
45	223	7754860742121533	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/esp-idf/esp_system/ld/memory.ld	8a25d0a0101ffaa1
4052	4406	7754860780475793	esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/src/gpspi/spi_slave.c.obj	db2da56d1151b7ab
877	1217	7754860748728745	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/port/os_xtensa.c.obj	4934efeec50c672
57	230	7754860742191453	esp-idf/esp_system/ld/sections.ld.in	2d6dd77abefc5dcd
2919	3295	7754860769150163	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/ip6.c.obj	8b4152da9341ff0f
712	1052	7754860747079838	esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/lib_printf.c.obj	f6c3851e063d2add
230	559	7754860742261451	esp-idf/esp_https_ota/CMakeFiles/__idf_esp_https_ota.dir/src/esp_https_ota.c.obj	7ac77574a0516a9c
448	782	7754860744443834	esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/transport_ws.c.obj	a2791d3ca22f4031
5885	6212	7754860798809865	esp-idf/esp_phy/libesp_phy.a	64220e58c5169a5f
7511	7854	7754860815061924	esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a	f91a9a7125cbfcfc
243	572	7754860742391442	esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/httpd_main.c.obj	29f3b0430ec7e4bd
6935	7257	7754860809310484	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/riscv/panic_arch.c.obj	1ae438acd9e0ea3
627	969	7754860746236814	esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/esp_tls.c.obj	3eb67b6ac9982fab
257	585	7754860742531460	esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/httpd_parse.c.obj	7ef1111a92b44549
270	600	7754860742661452	esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/httpd_sess.c.obj	e462964be358ac7d
2107	2502	7754860761036324	esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/esp_netif_handlers.c.obj	f675e448af67f228
915	1268	7754860749102587	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/ap_config.c.obj	d32bff2f9022abcf
283	614	7754860742791587	esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/httpd_txrx.c.obj	d1b0c02929e9d754
1218	1575	7754860752129186	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha384-tlsprf.c.obj	fd7a50051e77e91a
5256	5586	7754860792513638	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_modem.c.obj	9181d0faf474af46
13580	13898	7754860875763510	esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_lit.c.obj	7ab31d657dfca7cb
5412	5743	7754860794075690	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/gdma_link.c.obj	1627d674c6c5773a
14251	14578	7754860882473124	esp-idf/json/CMakeFiles/__idf_json.dir/cJSON/cJSON_Utils.c.obj	8e0b13f128b41326
810	1147	7754860748056931	esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/esp32c2/esp_adapter.c.obj	37d4d130de6d8284
244	418	7757410776149356	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader/bootloader.elf	2ee90b3d3529ba39
6024	6343	7754860800190550	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_level/log_level.c.obj	d735e97eeaa73174
755	1091	7754860747509826	esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/wifi_init.c.obj	671a5f0c20da23df
4822	5161	7754860788176479	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/sysconf.c.obj	c4e1fa3223788f36
299	627	7754860742946621	esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/httpd_uri.c.obj	f25a3233bb68ae2
3581	3922	7754860775768347	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/polarssl/md4.c.obj	7479ba1d8203ac54
8203	8534	7754860821990707	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/aria.c.obj	71077b9fa5e1341e
2533	2933	7754860765288110	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/memp.c.obj	74c9734a6eaccfba
325	656	7754860743216623	esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/util/ctrl_sock.c.obj	aed5d57ebe5a1fc7
338	669	7754860743347645	esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/esp_http_client.c.obj	15362a6ca979f81b
353	683	7754860743482762	esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/lib/http_auth.c.obj	e9503ba0b30fcddd
864	1204	7754860748602682	esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/src/coexist_debug.c.obj	294f9c05fe7a1c70
4938	5283	7754860789340599	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/list.c.obj	94b7a252e2c344d8
8517	8877	7754860825132850	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecp_curves.c.obj	90277dd0cce314e9
365	697	7754860743612751	esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/lib/http_header.c.obj	6a5d6f3fe96a7a04
379	712	7754860743756274	esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/lib/http_utils.c.obj	f4c3c9a96831e64c
407	741	7754860744027802	esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/transport_ssl.c.obj	5df6f3a2605caf09
6061	6381	7754860800575949	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_level/tag_log_level/cache/log_binary_heap.c.obj	790d992659a6330a
420	755	7754860744163896	esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/transport_internal.c.obj	105277186b5b4990
9002	9367	7754860829978080	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_hash.c.obj	740afae10c46d3d6
66	1251	7754860741367620	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/partition_table/partition-table.bin	2cd27533473a6d60
460	797	7754860744563821	esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/src/gdbstub.c.obj	865940c2851fc519
2281	2686	7754860762764076	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/err.c.obj	7a9ec42ecb277de
14867	15195	7754860888635147	esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/diskio/diskio_wl.c.obj	4936d7769b6f58a5
13421	13746	7754860874167966	esp-idf/console/CMakeFiles/__idf_console.dir/split_argv.c.obj	c4969d97623db390
9016	9381	7754860830118331	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_mac.c.obj	736aeca3b4a5ebb
472	810	7754860744677104	esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/src/gdbstub_transport.c.obj	3b56c27c9e05cd17
741	1078	7754860747369840	esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/smartconfig.c.obj	43516e19dc5fe15f
3020	3391	7754860770158727	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/bridgeif_fdb.c.obj	eb7d1368238c0692
484	824	7754860744797098	esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/src/packet.c.obj	7fe2918da20f6987
6011	6330	7754860800070500	esp-idf/log/CMakeFiles/__idf_log.dir/src/util.c.obj	85273c6c95e19bf4
497	837	7754860744927111	esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/src/port/riscv/gdbstub_riscv.c.obj	fb457dca5ce0366b
14085	14424	7754860880808843	esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/security/security1.c.obj	fbddb8027383818a
13514	13835	7754860875100381	esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_end.c.obj	b2e4f73d9d5c1c42
698	1039	7754860746939843	esp-idf/http_parser/CMakeFiles/__idf_http_parser.dir/http_parser.c.obj	8d37f99119fde479
9636	9736	7754860837253321	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-download	d99b2ff67b89a1df
509	850	7754860745047096	esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/src/port/riscv/rv_decode.c.obj	5815566ecba20afa
5894	6220	7754860798899845	esp-idf/heap/CMakeFiles/__idf_heap.dir/heap_caps_init.c.obj	25207d20784df379
954	1067	7757410783245433	esp-idf/main/libmain.a	d5b31f8b78e98364
797	1132	7754860747927748	esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/wifi_default_ap.c.obj	8460914f0bcb1c33
13074	13228	7754860872167063	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/x509_crt_bundle.S	afb1f9de0681ee80
769	1105	7754860747649833	esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/wifi_default.c.obj	cd9ab81b00b33a6c
521	863	7754860745168285	esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/adc_oneshot.c.obj	2ebdef92f5edafec
1284	1647	7754860752801425	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/md4-internal.c.obj	65f8efac4857656c
1647	2017	7754860756432875	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/wpa_debug.c.obj	b1b9311eaeb6589c
13849	14164	7754860878447987	esp-idf/unity/CMakeFiles/__idf_unity.dir/unity/src/unity.c.obj	636c848cb3ebbe1e
535	876	7754860745308410	esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/adc_common.c.obj	a70ec81cbccc290f
2137	2533	7754860761326521	esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/esp_netif_defaults.c.obj	246b50d0a0272573
547	891	7754860745428335	esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/adc_cali.c.obj	b2deacd3b0971d13
562	905	7754860745578332	esp-idf/esp_https_ota/libesp_https_ota.a	a218e05ccd5c817f
7988	8309	7754860819848073	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/D_/idf/Espressif/frameworks/esp-idf/components/mbedtls/port/mbedtls_debug.c.obj	7331e76bf4837820
5043	5387	7754860790382403	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/esp_additions/freertos_compatibility.c.obj	e7be931e8fa3c0db
5205	5540	7754860792015466	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/periph_ctrl.c.obj	c8bb0dc13c483ac2
572	915	7754860745678342	esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/adc_cali_curve_fitting.c.obj	9cc13268cce3902c
2702	3093	7754860766983230	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/autoip.c.obj	8e620847dd3ea87c
929	1283	7754860749247101	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/ieee802_1x.c.obj	f7035b108542d9c
600	942	7754860745958395	esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/adc_filter.c.obj	240d7599af36e63e
3745	4091	7754860777404562	esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/esp32c2/phy_init_data.c.obj	44728fc2b45bdfb9
13721	14035	7754860877167409	esp-idf/tools/CMakeFiles/__idf_tools.dir/hash_search/hash_search.c.obj	2eb49091199b810a
7334	7660	7754860813297767	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	31154dcb53912a80
7207	7534	7754860812024512	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	96cec898939b0dd4
585	928	7754860745808336	esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/deprecated/esp_adc_cal_common_legacy.c.obj	de5940829d6cbd9a
3250	3594	7754860772468096	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/magic.c.obj	198e5bf01452fa3c
3179	3532	7754860771744713	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/eui64.c.obj	ba72c7931d932063
5999	6318	7754860799950497	esp-idf/log/CMakeFiles/__idf_log.dir/src/buffer/log_buffers.c.obj	58b4e63ef381162e
614	955	7754860746096821	esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/esp32c2/adc_cali_line_fitting.c.obj	bbe782ed4eeeb10e
642	983	7754860746378696	esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/esp-tls-crypto/esp_tls_crypto.c.obj	d30cd0280dc5c83e
6779	7111	7754860807749630	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/ubsan.c.obj	ea59b6105e50d744
1375	1734	7754860753713056	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_mschapv2.c.obj	c88ded52ca5da24d
824	1164	7754860748196999	esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/smartconfig_ack.c.obj	4fcb85b42947f8db
656	996	7754860746518703	esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/esp_tls_error_capture.c.obj	3661244a4d5ec670
7220	7549	7754860812164517	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/esp_flash_api.c.obj	72973a2e933265e
3391	3719	7754860773869922	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/utils.c.obj	c473b1f7a44e6e8b
683	1025	7754860746788765	esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/esp_tls_mbedtls.c.obj	247ee690c2569a16
1026	1375	7754860750212178	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/common/sae.c.obj	613a2f6356533121
7446	7784	7754860814414929	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	95d868047484545b
5685	6023	7754860796809513	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	61f73f47720974a3
244	418	7757410776149356	bootloader/bootloader.map	2ee90b3d3529ba39
727	1065	7754860747229840	esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/mesh_event.c.obj	6649d1e00fabe60e
782	1119	7754860747787746	esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/wifi_netif.c.obj	8e301aa988027329
3351	3682	7754860773476107	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/pppol2tp.c.obj	83fc5a2fb22c81ab
2764	3146	7754860767595003	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/icmp.c.obj	8648e02946fc3c66
5599	5932	7754860795944289	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c2/rtc_sleep.c.obj	5a662e447c5a02c5
837	1177	7754860748326937	esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/esp32c2/esp_coex_adapter.c.obj	413a8c6c8797cb0
13240	13536	7754860872358047	esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/__/__/x509_crt_bundle.S.obj	d1f7f262fc1e1a3d
906	1242	7754860749020177	esp-idf/esp_http_server/libesp_http_server.a	568632c66d75fe9
4806	5148	7754860788016477	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/time.c.obj	af3bcf7efbf6122
5362	5685	7754860793580481	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c2/io_mux.c.obj	8e282b032df9d66e
6713	7048	7754860807086972	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_system.c.obj	8d85f27b37511408
850	1191	7754860748462930	esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/src/coexist_debug_diagram.c.obj	249daa60cd27c357
15540	15793	7754860895353278	esp-idf/esp_eth/libesp_eth.a	20e07c05e8989e50
891	1230	7754860748873496	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/port/eloop.c.obj	6528d935d5ba460
5972	6294	7754860799681144	esp-idf/log/CMakeFiles/__idf_log.dir/src/os/log_lock.c.obj	cdb0b07318048864
3618	3962	7754860776138721	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/apps/dhcpserver/dhcpserver.c.obj	4b00170117cb38b7
66	1251	7754860741367620	partition_table/partition-table.bin	2cd27533473a6d60
15516	15793	7754860895123275	esp-idf/app_trace/libapp_trace.a	975cfc2c7694f6b7
2047	2438	7754860760431015	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/des-internal.c.obj	2317380f225238fd
2610	3005	7754860766066605	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/sys.c.obj	79d357530cfae138
942	1296	7754860749377097	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/wpa_auth.c.obj	bdc66ba2b0a00141
159	260	7757410775292679	esp-idf/main/CMakeFiles/__idf_main.dir/charge_system/pixel_effect/common/pixel_waves.c.obj	21e74e2de578c374
955	1309	7754860749509891	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/wpa_auth_ie.c.obj	c5bfb1577ddad6bd
970	1322	7754860749651924	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/pmksa_cache_auth.c.obj	be4b3e26d860e231
6653	6723	7757410840237477	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/esp-idf/esptool_py/CMakeFiles/app_check_size	b7331fda4f4225c0
983	1336	7754860749787014	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/sta_info.c.obj	7187ff97ccf7de8a
4976	5323	7754860789720610	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/timers.c.obj	abc5a4e9f9e2fce
996	1349	7754860749927017	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/ieee802_11.c.obj	5e9f830d800435dd
1011	1362	7754860750072136	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/comeback_token.c.obj	64860c2d86d2207
14283	14603	7754860882793645	esp-idf/drivers/CMakeFiles/__idf_drivers.dir/ina226/drv_ina226.c.obj	7c97e23b973c5921
1039	1390	7754860750353334	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/common/dragonfly.c.obj	2e95426851a1cf50
7903	8229	7754860818988502	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls12_client.c.obj	984f0ed62cf54d2c
1052	1404	7754860750487512	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/common/wpa_common.c.obj	ee96348d6e9f59fb
3694	4038	7754860776902748	esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/src/phy_override.c.obj	d6806a831c4a294e
1705	2079	7754860757005851	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps_attr_parse.c.obj	19d15e51ed97806d
215	665	7757410775854266	esp-idf/main/CMakeFiles/__idf_main.dir/charge_system/charge_manager/cg_state_idle.c.obj	b23fc17ab04824b6
1065	1418	7754860750613378	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/bitfield.c.obj	c367172aff84153c
9526	9636	7754860836236291	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir	fda421644192696e
1078	1432	7754860750743961	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/aes-siv.c.obj	dfd957193b0ffced
8470	8824	7754860824661236	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecdsa.c.obj	71b7ea92c58549
1092	1444	7754860750871163	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha256-kdf.c.obj	7fe6dba6af9430de
1105	1459	7754860751011189	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/ccmp.c.obj	1fa0d88ad54bb0da
14591	14881	7754860885871029	esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/i2c/esp_lcd_panel_io_i2c_v2.c.obj	c564ecc23cabf8bd
5933	6257	7754860799285683	esp-idf/heap/CMakeFiles/__idf_heap.dir/port/esp32c2/memory_layout.c.obj	7c773055652473b
1119	1472	7754860751156318	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/aes-gcm.c.obj	1f6379f3b24a764a
8167	8483	7754860821627207	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/aesni.c.obj	5992fe3c5aa90f82
1132	1485	7754860751286349	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/crypto_ops.c.obj	ad92df50bbbec529
1148	1499	7754860751435937	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/dh_group5.c.obj	5d18ee0b89956adb
6221	6537	7754860802164520	esp-idf/hal/CMakeFiles/__idf_hal.dir/uart_hal_iram.c.obj	93ff1958cfcda1f8
6725	7061	7754860807208233	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/startup.c.obj	d2497cf31a708084
7186	7511	7754860811814503	esp-idf/esp_driver_tsens/libesp_driver_tsens.a	bd0e044f4e7ea7a6
1164	1514	7754860751601064	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/dh_groups.c.obj	d0152b53640ab0fd
1177	1530	7754860751728562	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/ms_funcs.c.obj	cc5dbf3dd5b4de0f
418	477	7757410777886995	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-install	9065d9935ca08086
8825	9181	7754860828201984	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pkparse.c.obj	7ed5a17c0c0004e
1192	1544	7754860751869719	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha1-tlsprf.c.obj	620822992e4fc06d
9526	9636	7754860836236291	bootloader-prefix/src/bootloader-stamp/bootloader-mkdir	fda421644192696e
1204	1561	7754860752001176	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha256-tlsprf.c.obj	6a1bd231dd7cd72b
12476	12743	7754860864727093	esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a	efb0833db2f62bd8
1230	1589	7754860752267346	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha256-prf.c.obj	389962191be2c713
1486	1854	7754860754813067	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_fast_common.c.obj	217599a1885c843
5647	5985	7754860796429506	esp-idf/esp_security/CMakeFiles/__idf_esp_security.dir/src/init.c.obj	5ff5d43872832b25
15798	15906	7754860897940997	esp-idf/boards/libboards.a	e20dfc16702d05ee
1243	1602	7754860752381357	esp-idf/esp_http_client/libesp_http_client.a	528cc25bfd07dc4b
1963	2332	7754860759595362	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/crypto/crypto_mbedtls.c.obj	355d8f95ca0f8fdf
5399	5727	7754860793955683	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/esp_dma_utils.c.obj	80f453fe56cc40bb
8346	8680	7754860823419042	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/chacha20.c.obj	7884c561a2eb4fc3
1252	1612	7754860752481364	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha1-prf.c.obj	8461a89a423d9479
4752	5096	7754860787485253	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/newlib_init.c.obj	4b0ce50b9f0d3cdc
3788	4130	7754860777834621	esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/timer_legacy.c.obj	e5275cdf2403d551
13996	14322	7754860879918169	esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/proto-c/sec0.pb-c.c.obj	483c1682c6ca1301
1268	1626	7754860752641435	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha384-prf.c.obj	90cecdf9c7e448b1
2876	3250	7754860768720162	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/ethip6.c.obj	f6cd9441de9d1227
5448	5785	7754860794440770	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/adc_share_hw_ctrl.c.obj	b2aa7e1ff3d7ce5
3829	4169	7754860778245213	esp-idf/esp_coex/libesp_coex.a	e6974df27c9e454d
1296	1661	7754860752928091	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha1-tprf.c.obj	db9be54a5f17b991
2121	2519	7754860761176326	esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/esp_netif_objects.c.obj	ef8a6a736091c84b
5069	5411	7754860790654365	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/esp_additions/idf_additions.c.obj	7ba6a14e8e222bee
1310	1676	7754860753058076	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_common/eap_wsc_common.c.obj	5d910ef308f2ff1
15086	15401	7754860890818852	esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/spiffs_api.c.obj	d057537dfdebb5c2
1322	1691	7754860753188090	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/common/ieee802_11_common.c.obj	7e95d213f01abe44
1349	1705	7754860753455531	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap.c.obj	1ad5b116243764de
1362	1721	7754860753573143	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_common.c.obj	4202e9b6663968a4
3707	4051	7754860777035094	esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/src/lib_printf.c.obj	9150804f2d611d11
6615	6947	7754860806110797	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj	422a52c4a591eba9
3962	4306	7754860779578284	esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sdmmc_init.c.obj	5007b628a9de1b19
6792	7123	7754860807879679	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/xt_wdt.c.obj	733dd279bf571f9a
13747	14060	7754860877427423	esp-idf/tools/CMakeFiles/__idf_tools.dir/pad_trigger/pad_trigger.c.obj	c1b6b543065e0f3f
1390	1748	7754860753853066	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_peap.c.obj	156489eafd7d5374
6564	6892	7754860805595257	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	f1c5304e6e98bdf2
8359	8694	7754860823549035	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/chachapoly.c.obj	15a6ac747443eaf7
1404	1762	7754860754003076	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_peap_common.c.obj	d0ec04dcd5d913d7
1418	1776	7754860754143142	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_tls.c.obj	c42f95fb70e42fff
1432	1793	7754860754283068	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_tls_common.c.obj	c6462dfd43f29537
4950	5296	7754860789460574	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/queue.c.obj	c25c3000098f937c
5387	5712	7754860793835682	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp_clk_tree_common.c.obj	2ea8f1bf3fd0ea91
4065	4421	7754860780614547	esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/src/gpspi/spi_dma.c.obj	57392845bb0acd58
9513	9555	7754860835090759	esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/library/Hacl_Curve25519_joined.c.obj	e79b8831df11ebd0
5026	5375	7754860790220562	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/portable/riscv/portasm.S.obj	8f08f6c53fbb758e
1445	1808	7754860754403057	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_ttls.c.obj	5673ce1e336fd59b
1459	1824	7754860754553060	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/mschapv2.c.obj	811319f4b967ea85
1472	1839	7754860754683060	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_fast.c.obj	b8130534139b0f16
1661	2032	7754860756572870	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/json.c.obj	5e146b7d9bcdbea7
6857	7185	7754860808526536	esp-idf/esp_driver_i2c/libesp_driver_i2c.a	7f98d96e594753cf
1500	1868	7754860754963064	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_fast_pac.c.obj	a6a478eea6d6e166
1514	1882	7754860755103066	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/rsn_supp/pmksa_cache.c.obj	fbe999c88405b810
14165	14502	7754860881606580	esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/WL_Ext_Perf.cpp.obj	8f4cb9fb0deb89e6
1531	1898	7754860755266673	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/rsn_supp/wpa.c.obj	b7ec2b03dfe152dd
6750	7086	7754860807460258	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/system_time.c.obj	be683fc14551db74
1545	1913	7754860755409952	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/rsn_supp/wpa_ie.c.obj	54b0fe4c254f90fc
6500	6829	7754860804962561	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	4bad258a0dae9afd
13923	14240	7754860879192916	esp-idf/unity/CMakeFiles/__idf_unity.dir/unity_utils_memory.c.obj	a15f6cf8b03785cd
4306	4662	7754860783023500	esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/default_event_loop.c.obj	988b305f6b86f6db
1561	1928	7754860755568184	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/base64.c.obj	1bbddcc3f48f2590
3869	4209	7754860778652175	esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/i2c_common.c.obj	c30cc7c4cb60d241
7271	7598	7754860812667239	esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/esp_mmu_map.c.obj	bf7ce2f2c587ef35
1576	1947	7754860755721056	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/common.c.obj	c5cbfc8f55627daf
1589	1962	7754860755851054	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/ext_password.c.obj	3240eada2c247ebc
1602	1978	7754860755981060	esp-idf/tcp_transport/libtcp_transport.a	bcd5e4b01650e36d
14616	14929	7754860886111088	esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/src/esp_local_ctrl.c.obj	55b7b7ea4bf7ec78
7124	7445	7754860811190329	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_mxic_opi.c.obj	f553e0182517c574
1612	1988	7754860756081045	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/uuid.c.obj	c28b347bd8cc684b
1626	2003	7754860756282873	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/wpabuf.c.obj	5b694c01db527c20
8125	8445	7754860821208744	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509write_crt.c.obj	4407a4f7ceea557b
1676	2046	7754860756722872	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps.c.obj	dec2ca2f898958f6
10790	10898	7754860847863974	esp-idf/log/liblog.a	f7eadb87cc629380
1692	2064	7754860756872868	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps_attr_build.c.obj	bcfb31694ff693bc
1721	2092	7754860757170211	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps_attr_process.c.obj	9f61328404a8a730
1734	2107	7754860757299981	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps_common.c.obj	8ccf16cedbc681eb
13935	14251	7754860879302921	esp-idf/unity/CMakeFiles/__idf_unity.dir/unity_port_esp32.c.obj	73a88eccedf8396a
7876	8203	7754860818718497	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_ticket.c.obj	d29ed22c35fa0f74
1749	2121	7754860757445983	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps_dev_attr.c.obj	da7e6657d5b1795b
4599	4937	7754860785948343	esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread_rwlock.c.obj	7a8a52a56937f9c8
6701	7036	7754860806966943	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/panic.c.obj	de80c77c4567a047
15073	15387	7754860890688835	esp-idf/rt/CMakeFiles/__idf_rt.dir/FreeRTOS_POSIX_utils.c.obj	8c7e57fd364c04d1
1883	2250	7754860758782835	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_wpa3.c.obj	64ef5d1565d37ad
14741	15060	7754860887368792	esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_sha.c.obj	7f0161942656f92f
1762	2137	7754860757588454	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps_enrollee.c.obj	be2351f5902c4220
8395	8738	7754860823919032	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/constant_time.c.obj	b8016e6ec0e13868
1776	2153	7754860757714082	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/common/sae_pk.c.obj	5d549239b6e24567
3295	3631	7754860772915142	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/ppp.c.obj	6eddf6ec8a457295
9352	9550	7754860833484652	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/idf/Espressif/frameworks/esp-idf/components/mbedtls/port/sha/block/sha.c.obj	5275413cfae5ef33
1793	2167	7754860757888096	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_eap_client.c.obj	43e9f0ca77224f03
14641	14957	7754860886371013	esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/proto-c/esp_local_ctrl.pb-c.c.obj	504b77d5a61f6b06
1808	2181	7754860758048078	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_wpa2_api_port.c.obj	ed769bf2757ad7ef
15155	15476	7754860891508740	esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/spiffs/src/spiffs_nucleus.c.obj	26a228e9a230a770
14126	14464	7754860881220283	esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/crypto/srp6a/esp_srp_mpi.c.obj	87b3f020c1e5b68e
1824	2194	7754860758198083	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_wpa_main.c.obj	2b01bc0db25d0c59
2264	2670	7754860762602171	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/api_msg.c.obj	10b1c0c455f1785c
13910	14226	7754860879062922	esp-idf/unity/CMakeFiles/__idf_unity.dir/unity_utils_cache.c.obj	88fb5bcdc6dba03
1839	2208	7754860758348129	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_wpas_glue.c.obj	c066b5a1722235c4
4191	4555	7754860781877973	esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_handle_locked.cpp.obj	293e3f4af1d866f7
15652	15795	7754860896480991	esp-idf/wifi_provisioning/libwifi_provisioning.a	a21fb0c2b4e080c3
1854	2222	7754860758505373	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_common.c.obj	bf6cbbcb59f21dc9
6443	6766	7754860804381668	esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_slave_hd_hal.c.obj	4ecb58234970921c
9830	9937	7754860839247981	bootloader-prefix/src/bootloader-stamp/bootloader-patch	5a549cb8073f9690
1868	2236	7754860758641835	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_wps.c.obj	22736547147ca685
14113	14452	7754860881090262	esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/crypto/srp6a/esp_srp.c.obj	206ecbea80973163
13823	14138	7754860878185133	esp-idf/tools/CMakeFiles/__idf_tools.dir/generate_iv/generate_iv.c.obj	a0ac87b2f2362fac
1899	2264	7754860758942862	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_owe.c.obj	e8c04d06b797fb8c
13265	13565	7754860872607945	esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/pm_trace.c.obj	75fca7434f0af5d8
1913	2280	7754860759093920	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_hostap.c.obj	5b49e5a1f2113736
8295	8627	7754860822904697	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/bignum_mod_raw.c.obj	5131cc01aeb4ca0a
2550	2950	7754860765479344	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/netif.c.obj	d6c375e7018f06f4
2933	3309	7754860769290166	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/ip6_addr.c.obj	376baf08a8c90937
3324	3657	7754860773196099	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/pppcrypt.c.obj	8003b49cdbd2a1c3
1928	2299	7754860759243931	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/crypto/tls_mbedtls.c.obj	357288c741517020
1947	2317	7754860759435273	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/crypto/fastpbkdf2.c.obj	53e61525321c22c1
1978	2346	7754860759745495	esp-idf/esp_gdbstub/libesp_gdbstub.a	9c04c5cd83e32330
3378	3707	7754860773739625	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/upap.c.obj	13d746162fecd5f6
8382	8723	7754860823789041	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/cipher_wrap.c.obj	a8ac20a004b99e56
1989	2355	7754860759845875	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/crypto/crypto_mbedtls-bignum.c.obj	cb946ff5719b5e8a
2003	2372	7754860759995882	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/crypto/crypto_mbedtls-rsa.c.obj	e90de9b665b40cad
2018	2389	7754860760135931	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/crypto/crypto_mbedtls-ec.c.obj	b5114a46da9f05a4
8534	8891	7754860825302845	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecp_curves_new.c.obj	fa96bdec1eb5e047
2032	2405	7754860760281028	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/rc4.c.obj	c300bde5acae67a1
13972	14296	7754860879673065	esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/common/protocomm.c.obj	c81bac442567999
1336	2424	7754860753320034	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/chap.c.obj	de8c95caae6c3026
14847	15169	7754860888429167	esp-idf/esp_https_server/libesp_https_server.a	6f9ac6cd3109acf4
2064	2457	7754860760601085	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/aes-wrap.c.obj	cce21380a0246d53
2079	2470	7754860760746537	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/aes-unwrap.c.obj	21872143ab612e80
5499	5836	7754860794950851	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/async_memcpy_gdma.c.obj	90ed3cd1ed10333a
2093	2484	7754860760886308	esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/aes-ccm.c.obj	4502c963df65a450
4334	4687	7754860783299235	esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/esp_event_private.c.obj	4766d9bc85ee8736
5541	5873	7754860795366785	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_clock_output.c.obj	bec3178559559ccc
2153	2549	7754860761490234	esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/lwip/esp_netif_lwip.c.obj	a9b0b7e931fcd4ab
2167	2566	7754860761625529	esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/lwip/esp_netif_sntp.c.obj	1c5e0d141cec3668
2181	2580	7754860761765704	esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/lwip/esp_netif_lwip_defaults.c.obj	a0a25d426da05666
4169	4531	7754860781647298	esp-idf/wpa_supplicant/libwpa_supplicant.a	c35951890b52b139
3266	3607	7754860772614206	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/mppe.c.obj	c42425f655cf8cab
3975	4320	7754860779706974	esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sdmmc_io.c.obj	426a255cf66f4b26
9381	9551	7754860833764646	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/idf/Espressif/frameworks/esp-idf/components/mbedtls/port/sha/block/esp_sha256.c.obj	8113aa143491dfc9
2194	2595	7754860761895566	esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/lwip/netif/wlanif.c.obj	2e9b6024a313a275
14212	14542	7754860882081647	esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/crc32.cpp.obj	e7b022e4fb69749b
13659	13971	7754860876544357	esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/protobuf-c/protobuf-c/protobuf-c.c.obj	b3b65ab5bb99cd72
4860	5205	7754860788564345	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/port/esp_time_impl.c.obj	d222da4d086eaf0
6161	6478	7754860801577971	esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_flash_hal_iram.c.obj	386c9f8ea5a84e98
2208	2610	7754860762037732	esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/lwip/netif/ethernetif.c.obj	87e90dd3ca539bb4
2796	3179	7754860767920300	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/ip4.c.obj	9c3f8cf183c5da6c
5985	6307	7754860799816230	esp-idf/log/CMakeFiles/__idf_log.dir/src/os/log_write.c.obj	e7b86962e3705b08
9498	9555	7754860834945596	esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/library/x25519.c.obj	9762041b79053862
2222	2624	7754860762182126	esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/lwip/netif/esp_pbuf_ref.c.obj	4c207671f885763c
5472	5810	7754860794680780	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/deprecated/gdma_legacy.c.obj	17aad2e6e6a87ca2
72	954	7757410774416000	esp-idf/main/CMakeFiles/__idf_main.dir/charge_system/charge_system.c.obj	59b62edcaf257a4e
2236	2639	7754860762322134	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/apps/sntp/sntp.c.obj	79453a7177729801
2250	2654	7754860762462133	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/api_lib.c.obj	5290db5a453ad1fb
5810	6136	7754860798065601	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c2/i2c_periph.c.obj	7c64c2526e16222b
11388	11519	7754860853835346	esp-idf/spi_flash/libspi_flash.a	504c53bada37dbcb
8505	8861	7754860825007305	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecp.c.obj	b97db5cea3d2610c
5460	5798	7754860794560848	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/gdma.c.obj	cdde9c2891fc90b
3235	3581	7754860772306318	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/lcp.c.obj	75ba854bdeddc84a
7519	7862	7754860815151655	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/idf/bootloader_sha.c.obj	7139a249a1f9951b
2299	2701	7754860762951456	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/if_api.c.obj	3aac09c6315e6c0
8043	8358	7754860820394285	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509.c.obj	c867e7703f84178a
2317	2719	7754860763136651	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/netbuf.c.obj	6503a3a652cfcaa
2333	2734	7754860763286064	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/netdb.c.obj	d9a0a300fd4ef311
2346	2751	7754860763427425	esp-idf/esp_adc/libesp_adc.a	33edaed7b1c633fd
3207	3557	7754860772030820	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/ipcp.c.obj	564054742278aaa7
5573	5906	7754860795694221	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c2/rtc_clk.c.obj	a8ffb2304425f6e
60	256	7757410774306008	esp-idf/main/CMakeFiles/__idf_main.dir/charge_base.c.obj	affa8081c83c0851
2355	2763	7754860763510144	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/netifapi.c.obj	ac04b028c9e5c49f
2373	2781	7754860763685224	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/sockets.c.obj	6ab2caeec5dba03f
7841	8167	7754860818376429	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_debug_helpers_generated.c.obj	22b58006428f94a5
6947	7270	7754860809428743	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/riscv/debug_helpers.c.obj	74c82e94fddf6584
7258	7586	7754860812531994	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_os_func_noos.c.obj	3e7879b29a2f54c
2389	2796	7754860763855224	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/tcpip.c.obj	e566c96aba00d06f
6653	6723	7757410840237477	esp-idf/esptool_py/CMakeFiles/app_check_size	b7331fda4f4225c0
6307	6627	7754860803024549	esp-idf/hal/CMakeFiles/__idf_hal.dir/gdma_hal_top.c.obj	8b66b192c91539d
2406	2810	7754860764015220	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/apps/sntp/sntp.c.obj	47c0c4b5a7b15692
6406	6725	7754860804021671	esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_hal_iram.c.obj	6b2b196c070dc68d
6689	7024	7754860806849547	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/int_wdt.c.obj	2798a913a27dfa5e
2424	2830	7754860764195450	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/apps/netbiosns/netbiosns.c.obj	9f87f7db0027720b
2438	2845	7754860764347669	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/def.c.obj	e7c598afe76e3458
2457	2861	7754860764527659	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/dns.c.obj	b04261a30b76e78f
2471	2876	7754860764667657	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/inet_chksum.c.obj	3a8a41a13943759
4834	5173	7754860788306474	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/realpath.c.obj	8fd627c674251cb3
14296	14912	7754860882917520	esp-idf/protocol/CMakeFiles/__idf_protocol.dir/protocol_tlvc/protocol_tlvc.c.obj	c6a54b595665963a
2484	2891	7754860764807652	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/init.c.obj	3e83d7f7fa5291a6
7473	7813	7754860814694401	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	3af864ac5fd186ba
6370	6688	7754860803651676	esp-idf/hal/CMakeFiles/__idf_hal.dir/sha_hal.c.obj	673049cd36f5d7dd
9098	9472	7754860830940417	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_its_file.c.obj	aa2c864c7e0c067c
2502	2905	7754860764977661	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ip.c.obj	deed4728e18f5e67
2519	2919	7754860765147651	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/mem.c.obj	9ba68468ea8848f1
2566	2963	7754860765622574	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/pbuf.c.obj	19831823ef256141
3839	4178	7754860778347798	esp-idf/esp_driver_ledc/CMakeFiles/__idf_esp_driver_ledc.dir/src/ledc.c.obj	448af37035e3efd
15008	15330	7754860890037138	esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/esp-mqtt/lib/mqtt_msg.c.obj	b58b478d0f7c4747
3135	3495	7754860771307491	esp-idf/http_parser/libhttp_parser.a	e5ccd9913233ec05
6640	6973	7754860806360796	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	b6d86a5a627a589f
14368	14665	7754860883643673	esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/port/port_uart.c.obj	a67c8328c99169f4
2581	2978	7754860765762451	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/raw.c.obj	7fd5dcc45c94548d
244	418	7757410776149356	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader/bootloader.map	2ee90b3d3529ba39
2595	2991	7754860765916602	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/stats.c.obj	afcaff495920c3d
13696	14009	7754860876921085	esp-idf/tools/CMakeFiles/__idf_tools.dir/hash_channel/channel_pool.c.obj	92de60253db0bb19
2624	3020	7754860766196600	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/tcp.c.obj	c9ba2601f3d8fc1f
2640	3035	7754860766356600	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/tcp_in.c.obj	d667328bf30e4eeb
2654	3049	7754860766506609	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/tcp_out.c.obj	8e301031347b4417
14139	14477	7754860881347802	esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/Partition.cpp.obj	83d6d70019a0bc4f
10575	10681	7754860845712328	esp-idf/soc/libsoc.a	1d3e7d12272c36ba
8445	8795	7754860824402372	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/dhm.c.obj	9dbea58b7f967258
2671	3065	7754860766666652	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/timeouts.c.obj	22b6749e2e44358
2686	3079	7754860766823238	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/udp.c.obj	d8af25b5323a974c
5798	6124	7754860797940526	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c2/ledc_periph.c.obj	d13533b944251d81
3337	3669	7754860773338493	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/pppoe.c.obj	cfd6897877e8ebca
2719	3107	7754860767153226	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/dhcp.c.obj	d14e20e3f99a48d1
2734	3121	7754860767304583	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/etharp.c.obj	48cd62e422627a14
3107	3471	7754860771025559	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/chap_ms.c.obj	ea837c5fc4addb4c
15580	15794	7754860895765460	esp-idf/espcoredump/libespcoredump.a	18d9d9f557e6e2cb
2751	3134	7754860767464938	esp-idf/esp-tls/libesp-tls.a	131a3db7042f0ed8
2781	3163	7754860767765037	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/igmp.c.obj	c3f7dc63a00a9e5e
14896	15220	7754860888926092	esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/src/ffunicode.c.obj	42d8da946fbd62ed
2810	3193	7754860768117159	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/ip4_napt.c.obj	7de6bd3b3ba6e730
6199	6512	7754860801954511	esp-idf/hal/CMakeFiles/__idf_hal.dir/uart_hal.c.obj	9ffa8d0245f03535
5122	5460	7754860791178819	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c2/cpu_region_protect.c.obj	43c52e44129b6419
2830	3207	7754860768267232	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/ip4_addr.c.obj	cc1a1c7bd3477c5d
4555	4897	7754860785505965	esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread.c.obj	6fc9d0cd1d4f7050
15504	15785	7754860895003273	esp-idf/service/CMakeFiles/__idf_service.dir/wifi_softap_tcpserver/tcp_client.c.obj	19d501fc8a9c6746
13437	13760	7754860874328315	esp-idf/console/CMakeFiles/__idf_console.dir/linenoise/linenoise.c.obj	2c42288a687e21eb
2845	3222	7754860768410093	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/ip4_frag.c.obj	9b09ab4a095198ca
2861	3235	7754860768567651	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/dhcp6.c.obj	83125243d8780163
3682	4025	7754860776775706	esp-idf/esp_vfs_console/CMakeFiles/__idf_esp_vfs_console.dir/vfs_console.c.obj	36d27550972158d9
2891	3266	7754860768870153	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/icmp6.c.obj	588ec088fcc9848
2906	3281	7754860769020161	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/inet6.c.obj	5ef8f4283e3262fc
3005	3377	7754860770006477	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/bridgeif.c.obj	933ab4ef4fc504e3
7574	7917	7754860815696284	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c2/esp_efuse_rtc_calib.c.obj	cd3b4ef897a969ff
5526	5861	7754860795216670	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/mspi_timing_tuning.c.obj	d6d3c67b7771b497
2950	3323	7754860769465276	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/ip6_frag.c.obj	4a5ff07abf608e8f
4727	5069	7754860787235254	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/getentropy.c.obj	5143d9055058513b
11055	11162	7754860850515976	esp-idf/esp_rom/libesp_rom.a	fd58243fb88c2322
2964	3337	7754860769601341	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/mld6.c.obj	8a6ea4c70a507dda
2978	3351	7754860769746405	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/nd6.c.obj	750eb333707f450c
6817	7148	7754860808134633	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/task_wdt/task_wdt_impl_esp_timer.c.obj	aed4d520809b3b77
3093	3458	7754860770888224	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/chap-new.c.obj	659f138e7430e9f2
2991	3364	7754860769876450	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ethernet.c.obj	6b6079c6c1ecc57c
5097	5435	7754860790927265	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c2/esp_cpu_intr.c.obj	cbab7adea12df561
5148	5484	7754860791437029	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/clk_ctrl_os.c.obj	65bce93ecde8bb62
3035	3406	7754860770303948	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/slipif.c.obj	5faa44afdda7e1b5
4585	4924	7754860785808261	esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread_local_storage.c.obj	4d2cc67715ed816e
3049	3420	7754860770453964	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/auth.c.obj	79ac07c2aeb342f4
6112	6430	7754860801083952	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	7e8e898f08790a9c
4390	4739	7754860783852257	esp-idf/esp_driver_gptimer/CMakeFiles/__idf_esp_driver_gptimer.dir/src/gptimer.c.obj	f2c2dd3717fa3d6e
5823	6150	7754860798195602	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c2/temperature_sensor_periph.c.obj	3a10a2fd5afb7560
3066	3432	7754860770619041	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/ccp.c.obj	7403d5c6bee6d3e4
5660	5998	7754860796559581	esp-idf/esp_security/CMakeFiles/__idf_esp_security.dir/src/esp_crypto_lock.c.obj	7fd5abe76675bd2d
3079	3445	7754860770756875	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/chap-md5.c.obj	91c3514c8b664845
14781	15098	7754860887770913	esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/diskio/diskio.c.obj	45810836ca84b337
3122	3483	7754860771177446	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/demand.c.obj	38daf819c13d0d18
5484	5823	7754860794800774	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/esp_async_memcpy.c.obj	aff6991734a7c4b9
3146	3505	7754860771419959	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/eap.c.obj	f67fd597a7dfdebc
6406	6653	7757410840163924	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/.bin_timestamp	b06e2e4cb4a58ec6
3163	3518	7754860771593596	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/ecp.c.obj	903a6689fecabb0e
4611	4950	7754860786078274	esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread_semaphore.c.obj	de951a56dd45b061
3193	3544	7754860771894717	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/fsm.c.obj	c854a583da4e402c
4130	4490	7754860781258628	esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_page.cpp.obj	ffa6194704191a11
244	418	7757410776149356	bootloader-prefix/src/bootloader-stamp/bootloader-build	2ee90b3d3529ba39
7945	8270	7754860819410029	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls13_server.c.obj	11f3034061945c30
3222	3569	7754860772177874	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/ipv6cp.c.obj	aef13a11297811cd
3281	3618	7754860772774250	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/multilink.c.obj	ade0fd5258a25098
3309	3643	7754860773045148	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/pppapi.c.obj	5b25f2a3488553b2
4792	5135	7754860787876477	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/stdatomic.c.obj	12d0fee2c922357b
9296	9548	7754860832922465	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/idf/Espressif/frameworks/esp-idf/components/mbedtls/port/esp_hardware.c.obj	ca4ca92b188511e9
3804	4142	7754860777999723	esp-idf/driver/CMakeFiles/__idf_driver.dir/i2c/i2c.c.obj	c3a1788164dd34c1
3364	3694	7754860773595493	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/pppos.c.obj	7bc05ca6acac609e
3407	3732	7754860774029536	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/vj.c.obj	13ae38f63dad7bfb
14930	15251	7754860889256088	esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/diskio/diskio_sdmmc.c.obj	4839523c69fc707b
3420	3745	7754860774155168	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/hooks/tcp_isn_default.c.obj	627550de702e08a6
3631	3975	7754860776268875	esp-idf/vfs/CMakeFiles/__idf_vfs.dir/vfs.c.obj	927b9592176d410a
15490	15784	7754860894853336	esp-idf/service/CMakeFiles/__idf_service.dir/firmware_ota/firmware_ota.c.obj	24a0bf57f3573028
3446	3771	7754860774420279	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/debug/lwip_debug.c.obj	8b6d76e454e9592c
3458	3787	7754860774540285	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/sockets_ext.c.obj	75a4d7c4704eb6a8
6999	7321	7754860809953790	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32c2/system_internal.c.obj	34f1c38a03ad67c8
7827	8154	7754860818234773	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_cookie.c.obj	8d038fdb1342f29f
3471	3803	7754860774670333	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/freertos/sys_arch.c.obj	45917713122a675b
14566	14856	7754860885621014	esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_panel_ops.c.obj	f0a9f03297a3c14b
3483	3816	7754860774790428	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/acd_dhcp_check.c.obj	99c3d4dc6185eabb
4223	4585	7754860782186757	esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_partition_lookup.cpp.obj	576c14bdea878791
6137	6455	7754860801322915	esp-idf/hal/CMakeFiles/__idf_hal.dir/color_hal.c.obj	9fd281b49667f75b
6601	6935	7754860805970809	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	e18dca1811314eaa
13709	14022	7754860877051084	esp-idf/tools/CMakeFiles/__idf_tools.dir/hash_channel/channel_table.c.obj	ef7ba7bae18d5761
3495	3829	7754860774914476	esp-idf/esp_wifi/libesp_wifi.a	607e620a0357a502
4450	4791	7754860784454326	esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/ets_timer_legacy.c.obj	d821fbd5ae00aab7
8588	8944	7754860825839807	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/gcm.c.obj	74cc2e04d0268ed0
3505	3838	7754860775014257	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/esp32xx/vfs_lwip.c.obj	20019cc3a54159b9
6075	6394	7754860800711825	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	89203c371767e034
3909	4252	7754860779051482	esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/src/sdspi_host.c.obj	4711f608a860a5eb
5084	5423	7754860790795692	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	7820255bf016b33a
3518	3851	7754860775139328	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/apps/ping/esp_ping.c.obj	c11238c5018a0de
3532	3868	7754860775275854	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/apps/ping/ping.c.obj	99cf5adb655566cb
477	618	7757410779817225	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-done	bdc918f166c60ce3
13536	13862	7754860875327903	esp-idf/mbedtls/libmbedtls.a	a8d8fec98b8624df
3544	3883	7754860775405890	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/apps/ping/ping_sock.c.obj	950589f4e8f8eeeb
4079	4434	7754860780748508	esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/src/gpspi/spi_slave_hd.c.obj	f06501ec956e81cb
14532	14828	7754860885273947	esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_panel_nt35510.c.obj	fb50bdac366d1a8b
3557	3896	7754860775538359	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/polarssl/arc4.c.obj	3042ca582d089470
6431	6750	7754860804261764	esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_slave_hal_iram.c.obj	59b0c97a15d7f14e
15208	15524	7754860892033631	esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/wifi_ctrl.c.obj	a02c63db15b00489
3569	3909	7754860775648362	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/polarssl/des.c.obj	d92973e971e8cc56
3594	3935	7754860775898361	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/polarssl/md5.c.obj	e952428ec3624031
5772	6099	7754860797680481	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c2/gdma_periph.c.obj	7ca58fc26b2817c5
8216	8548	7754860822120984	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/asn1parse.c.obj	9c34b7a9fd042c95
5849	6174	7754860798441243	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c2/wdt_periph.c.obj	c6b7fca20076a098
3607	3949	7754860776028642	esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/polarssl/sha1.c.obj	28f1c97d8a681484
4155	4517	7754860781507293	esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_storage.cpp.obj	f408803db5c1e227
9830	9937	7754860839247981	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch	5a549cb8073f9690
3644	3987	7754860776393882	esp-idf/vfs/CMakeFiles/__idf_vfs.dir/vfs_eventfd.c.obj	4fe3d4ef8f42a38f
477	618	7757410779817225	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/CMakeFiles/bootloader-complete	bdc918f166c60ce3
3657	4000	7754860776533880	esp-idf/vfs/CMakeFiles/__idf_vfs.dir/vfs_semihost.c.obj	a96a1f7606479f78
5000	5349	7754860789960414	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/stream_buffer.c.obj	d544511f36330453
5510	5848	7754860795060867	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c2/systimer.c.obj	51d704f27dbd4832
3669	4012	7754860776653477	esp-idf/vfs/CMakeFiles/__idf_vfs.dir/nullfs.c.obj	2b0c16609f5b7d3b
3720	4065	7754860777159430	esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/src/phy_common.c.obj	a0299189ffe9622f
5785	6112	7754860797810480	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c2/spi_periph.c.obj	7196294482336f2
3732	4079	7754860777284522	esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/src/phy_init.c.obj	5e506c05fb8392f7
9140	9512	7754860831356836	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/rsa.c.obj	b75216513a1159d9
5173	5510	7754860791687032	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/intr_alloc.c.obj	4491ab6faa100608
3758	4105	7754860777539924	esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/src/btbb_init.c.obj	edc09aef3952771c
4350	4700	7754860783462242	esp-idf/esp_driver_uart/CMakeFiles/__idf_esp_driver_uart.dir/src/uart.c.obj	f850f5f8dd3fb7de
3771	4118	7754860777669992	esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/adc_legacy.c.obj	3a3c3212106cc10e
3816	4155	7754860778119724	esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/rtc_temperature_legacy.c.obj	5330fb5dc341ddf3
3852	4191	7754860778473928	esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/i2c_master.c.obj	7ffdb43513f45cc2
3883	4223	7754860778787277	esp-idf/esp_driver_tsens/CMakeFiles/__idf_esp_driver_tsens.dir/src/temperature_sensor.c.obj	f0c8facb49ab2f67
4265	4624	7754860782613461	esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_platform.cpp.obj	cd0cc84157356921
3896	4238	7754860778920212	esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/src/sdspi_crc.c.obj	e282be4cbd58ad00
13311	13621	7754860873070881	esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/src/rtc_io.c.obj	3ebc18f17ed46900
3936	4279	7754860779318197	esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sdmmc_cmd.c.obj	89e13bac52f63ac
14452	14740	7754860884477449	esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/src/esp_eth_netif_glue.c.obj	5ff394c541f2faf3
3950	4293	7754860779458191	esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sdmmc_common.c.obj	1d4b51a65d420a0f
4924	5270	7754860789200654	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/port_systick.c.obj	fe121635b981a957
6525	6856	7754860805213737	esp-idf/esp_driver_ledc/libesp_driver_ledc.a	714d36ce5071436e
15401	15699	7754860893976613	esp-idf/boards/CMakeFiles/__idf_boards.dir/board_support/bsp_gpio_ctrl.c.obj	d967b4589c1ae4c4
3987	4334	7754860779828385	esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sdmmc_mmc.c.obj	5ecf781c562d77ad
14478	14767	7754860884742786	esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/src/esp_hidh.c.obj	7a99fb368e4e9fbb
4000	4350	7754860779960469	esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sdmmc_sd.c.obj	28cba01aeb1d3fca
4013	4363	7754860780083665	esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sd_pwr_ctrl/sd_pwr_ctrl.c.obj	5bae6e168bdaa174
6664	6999	7754860806590810	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_ipc.c.obj	3127e5ea0b76bfa9
4478	4821	7754860784741474	esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/esp_timer_impl_common.c.obj	2fab7bf277431605
4025	4377	7754860780213750	esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/src/gpspi/spi_common.c.obj	2d25629b67b22bbd
4038	4389	7754860780340709	esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/src/gpspi/spi_master.c.obj	ef3790272c0e54ec
4091	4448	7754860780878496	esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_api.cpp.obj	d7e0ab22a9c8824d
4407	4752	7754860784022246	esp-idf/esp_driver_gptimer/CMakeFiles/__idf_esp_driver_gptimer.dir/src/gptimer_common.c.obj	e0f2b6089eebc086
477	618	7757410779817225	CMakeFiles/bootloader-complete	bdc918f166c60ce3
15280	15567	7754860892753628	esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/proto-c/wifi_scan.pb-c.c.obj	27b60c0a16898275
4105	4465	7754860781008498	esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_cxx_api.cpp.obj	bd151527dddee175
83	257	7757410774536001	esp-idf/main/CMakeFiles/__idf_main.dir/charge_system/wifi_client/wifi_client.c.obj	210f806d53cbe762
4118	4478	7754860781138497	esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_item_hash_list.cpp.obj	13a1c5b53acac17d
4142	4504	7754860781387306	esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_pagemanager.cpp.obj	57a9b9604b4f7640
4178	4540	7754860781747298	esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_handle_simple.cpp.obj	60c4757ab08b1e42
4210	4569	7754860782056381	esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_partition.cpp.obj	cbd753e0e29d2ef2
4238	4597	7754860782343466	esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_partition_manager.cpp.obj	40df65e2eb8d21d
244	418	7757410776149356	bootloader/bootloader.elf	2ee90b3d3529ba39
4252	4611	7754860782483474	esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_types.cpp.obj	cebe694497b87c06
4279	4637	7754860782753459	esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_bootloader.c.obj	253d8b3ce9ac221a
4638	4976	7754860786338276	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/assert.c.obj	18ecd247b2b4feab
7622	7973	7754860816184735	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	f3671f080ed2a26c
4320	4674	7754860783162779	esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/esp_event.c.obj	4ff48c143c007ba9
8334	8666	7754860823296222	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ccm.c.obj	9843973eef988331
4364	4712	7754860783592343	esp-idf/esp_driver_uart/CMakeFiles/__idf_esp_driver_uart.dir/src/uart_vfs.c.obj	84f48d2e047a31fc
7036	7359	7754860810321414	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_drivers.c.obj	26c3c78f67bdfa9b
4377	4727	7754860783732234	esp-idf/esp_ringbuf/CMakeFiles/__idf_esp_ringbuf.dir/ringbuf.c.obj	c3b6989765a499e9
9283	9548	7754860832792461	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/version_features.c.obj	6c791cae81ed7450
4421	4764	7754860784177954	esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/esp_timer.c.obj	5fd7e1f102574553
15236	15540	7754860892323650	esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/handlers.c.obj	8a3f2edab26e25b8
4435	4778	7754860784301185	esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/esp_timer_init.c.obj	ba283e55e47846ea
7586	7931	7754860815816229	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c2/esp_efuse_utility.c.obj	4ba4286e268927ae
4465	4806	7754860784610497	esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/system_time.c.obj	d176955bc1b6e986
4490	4834	7754860784860532	esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/esp_timer_impl_systimer.c.obj	ef60d0f4c87d8509
13836	14152	7754860878315143	esp-idf/tools/CMakeFiles/__idf_tools.dir/pcm_convert/pcm_convert.c.obj	4817b35bcce17240
10898	11055	7754860848942118	esp-idf/hal/libhal.a	2792b7ee722a7308
4505	4847	7754860785010477	esp-idf/cxx/CMakeFiles/__idf_cxx.dir/cxx_exception_stubs.cpp.obj	e7e537781ed8bc53
15143	15464	7754860891388741	esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/spiffs/src/spiffs_hydrogen.c.obj	70273c08e023639f
14716	15035	7754860887111087	esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_elf.c.obj	50d4b23d8cb8fdd9
4518	4860	7754860785134978	esp-idf/cxx/CMakeFiles/__idf_cxx.dir/cxx_guards.cpp.obj	2da7d7d81fbc6c4f
4765	5108	7754860787605248	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/syscalls.c.obj	f964fe4069d3791c
6805	7135	7754860808014650	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/task_wdt/task_wdt.c.obj	3db15fe17ace8bcd
4532	4873	7754860785275966	esp-idf/esp_netif/libesp_netif.a	17a487b9cfc3faed
4541	4882	7754860785365965	esp-idf/cxx/CMakeFiles/__idf_cxx.dir/cxx_init.cpp.obj	ef4807d904c7000c
4570	4911	7754860785652948	esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread_cond_var.c.obj	17b56b6e2d4b3ea1
5135	5472	7754860791309590	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_clk.c.obj	ffdd91b854895af
5553	5885	7754860795483571	esp-idf/esp_vfs_console/libesp_vfs_console.a	191ed7f39d2ab5c8
4624	4963	7754860786208267	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/abort.c.obj	307920fe77b40010
4650	4988	7754860786458257	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/heap.c.obj	82a8843cc9f63f9b
5323	5647	7754860793194620	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/regi2c_ctrl.c.obj	fbc9bdb627de334b
15567	15794	7754860895635462	esp-idf/esp_local_ctrl/libesp_local_ctrl.a	826dc4fb110814d8
4662	5000	7754860786585248	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/flockfile.c.obj	ed3c0597ce441590
4675	5013	7754860786705253	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/locks.c.obj	b0efd8eb65a4d136
4700	5043	7754860786965262	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/pthread.c.obj	aa447507f5f0c1bc
6893	7220	7754860808886268	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/image_process.c.obj	423613977046d416
4713	5056	7754860787085256	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/random.c.obj	79da95153a4627a9
6048	6369	7754860800445777	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_level/tag_log_level/linked_list/log_linked_list.c.obj	eec4728bba2724d5
7135	7460	7754860811319884	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_th.c.obj	2b8e8b08f8105f48
5727	6061	7754860797235200	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c2/uart_periph.c.obj	ca3312a74638f35d
4740	5084	7754860787355253	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/reent_init.c.obj	8b4b6752ab9f5d3d
5242	5573	7754860792383121	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/rtc_module.c.obj	17f86d19ff0565ff
4778	5121	7754860787735285	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/termios.c.obj	7c7c8203769be8e7
4847	5187	7754860788423991	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/scandir.c.obj	f7aff88c85cafda8
4873	5219	7754860788690580	esp-idf/lwip/liblwip.a	1fbc6cd24208e1fd
4882	5228	7754860788780579	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/heap_idf.c.obj	1b936c62529c1998
8931	9296	7754860829264285	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_cipher.c.obj	29e2b35634938f4
4897	5242	7754860788930587	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/app_startup.c.obj	71e860ccff74261f
5270	5598	7754860792663123	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_modes.c.obj	700c39b90751f11b
14913	15236	7754860889086080	esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/port/freertos/ffsystem.c.obj	c4d7290466c42144
12161	12244	7754860861573992	esp-idf/esp_app_format/libesp_app_format.a	e7599101f8c58a0a
4911	5256	7754860789080582	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/port_common.c.obj	fc8d8bc084d5b743
9737	9830	7754860838184091	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-update	b34333832106e666
4963	5308	7754860789590576	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/tasks.c.obj	ff812c209272eb5e
15464	15756	7754860894593291	esp-idf/boards/CMakeFiles/__idf_boards.dir/peripheral/hardware_link.c.obj	c1f6100b8c71b724
4988	5337	7754860789840405	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/event_groups.c.obj	5efa60a9bfe40650
5013	5362	7754860790090542	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/portable/riscv/port.c.obj	f971df1bd58f6c91
6960	7283	7754860809558636	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/riscv/debug_stubs.c.obj	11886eb3dfb2417f
5056	5399	7754860790517899	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/esp_additions/idf_additions_event_groups.c.obj	c477ae363cc9afd3
14048	14383	7754860880436523	esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/transports/protocomm_console.c.obj	cdc34e3a15338415
5109	5447	7754860791048820	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	f42e26284c590fca
6627	6959	7754860806230797	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	5951c41d2cbdbf8c
7346	7674	7754860813419897	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	ca96a553c892f54d
5161	5499	7754860791567038	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/hw_random.c.obj	e5d6bdc44f7d7e55
5188	5525	7754860791837114	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/mac_addr.c.obj	2d51fdeab7f5cae4
12827	12932	7754860868232165	esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a	98f517464361cad4
5219	5553	7754860792155529	esp-idf/vfs/libvfs.a	92ca67e31c538595
5229	5562	7754860792245514	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/revision.c.obj	d79584edfc6e675c
14521	14819	7754860885173796	esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_panel_io.c.obj	3c63def48f6d7050
12244	12369	7754860862399750	esp-idf/mbedtls/mbedtls/library/libmbedtls.a	26a423f6e1d93ba8
5283	5610	7754860792793135	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_console.c.obj	a0a9df2bbc80a98
5297	5622	7754860792923116	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_gpio.c.obj	27607a30d0233ac2
6651	6986	7754860806470899	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/crosscore_int.c.obj	4b862573fee86af6
14881	15208	7754860888766086	esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/src/ff.c.obj	2e92b486c6005916
6356	6676	7754860803521663	esp-idf/hal/CMakeFiles/__idf_hal.dir/ecc_hal.c.obj	c332e8e0555668f7
14984	15305	7754860889807114	esp-idf/json/libjson.a	2b0e0aa96ee535cc
6467	6791	7754860804627801	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32c2/rtc_cntl_hal.c.obj	ca41c4b79c391b61
5337	5659	7754860793334543	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_gpio_reserve.c.obj	ddf3e6ba25e52813
15524	15793	7754860895203275	esp-idf/cmock/libcmock.a	250b06ca88ab4c2b
5350	5672	7754860793460464	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sar_periph_ctrl_common.c.obj	b4fcde8f168c16df
6212	6525	7754860802084536	esp-idf/driver/libdriver.a	46a4c4a802c8c2e7
8723	9084	7754860827195396	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/padlock.c.obj	744ab837bdb5ea63
5375	5699	7754860793705683	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c2/esp_clk_tree.c.obj	39f5589fe6e16984
13798	14113	7754860877937422	esp-idf/tools/CMakeFiles/__idf_tools.dir/color_spcae/hsv_to_rgb.c.obj	9386033ee23dee26
5635	5972	7754860796309506	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c2/sar_periph_ctrl.c.obj	73f6152341dfa6f3
5423	5756	7754860794190875	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/spi_share_hw_ctrl.c.obj	94be997490989c55
8244	8574	7754860822401056	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/base64.c.obj	7662ef620645432
5435	5772	7754860794310778	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/spi_bus_lock.c.obj	2cf8fe523b8205d1
5562	5894	7754860795574292	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c2/rtc_clk_init.c.obj	8f84e7b06fbec3a0
5586	5919	7754860795824222	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c2/rtc_init.c.obj	256417ed357a32fb
6575	6905	7754860805705740	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_tlsf.c.obj	b95e13ad19b46709
5610	5946	7754860796064291	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c2/rtc_time.c.obj	4f166333c44576d
5672	6011	7754860796679510	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	f413205122f34f3
6676	7012	7754860806720845	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/freertos_hooks.c.obj	14e4ead79b085d28
5699	6036	7754860796944805	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c2/interrupts.c.obj	1e8a3bf2850d4eb5
5712	6048	7754860797085193	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c2/gpio_periph.c.obj	493bad734b00c00d
188	547	7757410775582684	esp-idf/main/CMakeFiles/__idf_main.dir/charge_system/charge_manager/cg_state_fast.c.obj	b02327e293a922a3
5743	6075	7754860797395171	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c2/adc_periph.c.obj	f8d7b5cc3b3f98f9
14703	15022	7754860886991017	esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_uart.c.obj	58e8d51cba3df2e3
5757	6087	7754860797550561	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c2/dedic_gpio_periph.c.obj	8fb9adef3d665d58
5836	6161	7754860798325611	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c2/timer_periph.c.obj	388b9e6fcf2381a0
6087	6406	7754860800833960	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	319bdf51a69ab1a4
5861	6187	7754860798571682	esp-idf/heap/CMakeFiles/__idf_heap.dir/heap_caps_base.c.obj	d09ec5f17b67e7bf
13380	13696	7754860873765384	esp-idf/riscv/CMakeFiles/__idf_riscv.dir/interrupt_intc.c.obj	a0a5fc44a4d41b90
8457	8812	7754860824531238	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecdh.c.obj	c8fe18ed96c81cd5
5873	6199	7754860798689806	esp-idf/heap/CMakeFiles/__idf_heap.dir/heap_caps.c.obj	d67a8c09fb537f9f
5959	6282	7754860799551002	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj	d884b8dcff5e4ed
13369	13684	7754860873651711	esp-idf/riscv/CMakeFiles/__idf_riscv.dir/vectors.S.obj	4ef30dc229048
5906	6233	7754860799020112	esp-idf/heap/CMakeFiles/__idf_heap.dir/multi_heap.c.obj	c311c5a72c3796eb
5919	6245	7754860799145690	esp-idf/heap/CMakeFiles/__idf_heap.dir/port/memory_layout_utils.c.obj	481c08ff88c7acbb
9267	9547	7754860832632460	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/version.c.obj	f0bc8954349333af
5947	6270	7754860799425676	esp-idf/log/CMakeFiles/__idf_log.dir/src/os/log_timestamp.c.obj	6e74ead91e139ffd
6036	6356	7754860800315796	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_level/tag_log_level/tag_log_level.c.obj	89cddb969ce13ef8
9937	18650	7754860926381872	bootloader-prefix/src/bootloader-stamp/bootloader-configure	6dd5de3dcaaa64b3
6099	6419	7754860800953970	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32c2/efuse_hal.c.obj	16499657399fca91
8548	8905	7754860825446699	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/entropy.c.obj	7a730d7ac262acbd
6125	6442	7754860801202906	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	860c82794ad26aef
6150	6467	7754860801457986	esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_flash_hal.c.obj	632604e2318a9c44
6187	6500	7754860801834515	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32c2/clk_tree_hal.c.obj	c6aaa665c1b430eb
14186	14521	7754860881818961	esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/WL_Ext_Safe.cpp.obj	6b5a5d76998dc959
6233	6550	7754860802284519	esp-idf/hal/CMakeFiles/__idf_hal.dir/gpio_hal.c.obj	f849ba6858be5d1e
8084	8395	7754860820791374	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509_crt.c.obj	abc907a8fc42b978
6245	6564	7754860802414544	esp-idf/hal/CMakeFiles/__idf_hal.dir/timer_hal.c.obj	80c76c7bacf25f6f
15414	15713	7754860894096625	esp-idf/boards/CMakeFiles/__idf_boards.dir/board_support/bsp_gpio_detect.c.obj	213c2442bc6941fa
12744	12827	7754860867394001	esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a	a2dd82edede5c400
6257	6575	7754860802534566	esp-idf/hal/CMakeFiles/__idf_hal.dir/ledc_hal.c.obj	22af1c1cc908f5fc
6270	6589	7754860802659758	esp-idf/hal/CMakeFiles/__idf_hal.dir/ledc_hal_iram.c.obj	4811274175cf3f1f
8562	8918	7754860825576711	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/entropy_poll.c.obj	1cdc5060f452bfa9
6282	6601	7754860802775863	esp-idf/hal/CMakeFiles/__idf_hal.dir/i2c_hal.c.obj	4aa31806c5e65657
15360	15652	7754860893556620	esp-idf/boards/CMakeFiles/__idf_boards.dir/board_support/bsp_nvs.c.obj	4afe374bbd13be67
15048	15360	7754860890438478	esp-idf/nvs_sec_provider/CMakeFiles/__idf_nvs_sec_provider.dir/nvs_sec_provider.c.obj	ffab4ba43cd2bff3
6295	6615	7754860802905774	esp-idf/hal/CMakeFiles/__idf_hal.dir/i2c_hal_iram.c.obj	78fba21eb57eaac0
13333	13647	7754860873290510	esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/src/gpio_pin_glitch_filter.c.obj	9108d23890301977
6331	6651	7754860803265672	esp-idf/hal/CMakeFiles/__idf_hal.dir/adc_hal_common.c.obj	3e6655cc18c5879b
6382	6701	7754860803771679	esp-idf/hal/CMakeFiles/__idf_hal.dir/brownout_hal.c.obj	3d528d313567cd9
13565	13883	7754860875603572	esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_int.c.obj	70369b1bfdea0ae
6394	6712	7754860803891676	esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_hal.c.obj	80555582b6959e34
14753	15073	7754860887490916	esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_crc.c.obj	a842ccc43f63d4e1
6419	6738	7754860804151678	esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_slave_hal.c.obj	310973bfe2b0e038
6456	6779	7754860804511663	esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_flash_hal_gpspi.c.obj	fd8a0858d78b0607
6478	6805	7754860804741947	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	d7eee6b8878c9438
6489	6817	7754860804851961	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj	6fa64b655d5ce3e3
15640	15794	7754860896360995	esp-idf/spiffs/libspiffs.a	683e9359e7718134
14338	14641	7754860883346245	esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/app_trace_util.c.obj	d94a640a9448d40e
9973	10072	7754860839683663	esp-idf/pthread/libpthread.a	a932ec4a3ad077e9
6738	7074	7754860807340260	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/startup_funcs.c.obj	b04f08184e3e275c
6513	6842	7754860805083653	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	6d34332985d5a0b4
6538	6865	7754860805333670	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	5d689f6125d8b8d9
6880	7207	7754860808759471	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/esp_system_chip.c.obj	4b952ef57636c9e1
14240	14565	7754860882362663	esp-idf/json/CMakeFiles/__idf_json.dir/cJSON/cJSON.c.obj	97c357f60a99f2f0
6550	6879	7754860805459926	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	b7b1fc70f6e06d07
14767	15086	7754860887630918	esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/port/riscv/core_dump_port.c.obj	dc3a4393b77cadb3
9235	9546	7754860832307670	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/threading.c.obj	9a52855e51e08d65
6589	6919	7754860805850799	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_multi_heap.c.obj	f752fd0817b7f489
6766	7099	7754860807620257	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/stack_check.c.obj	a2b729b00c99b31d
6866	7194	7754860808613171	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/panic_handler.c.obj	e9d3f66ab92f3cd5
6830	7160	7754860808254651	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/hw_stack_guard.c.obj	9747482ada354fbf
6842	7173	7754860808380347	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/cpu_start.c.obj	d638bece7b1d4eb
6919	7245	7754860809148847	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/riscv/expression_with_stack.c.obj	c2192fd095c4a71f
6973	7297	7754860809688670	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32c2/clk.c.obj	b65ff55b501bf1ce
6986	7309	7754860809823765	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32c2/reset_reason.c.obj	badabbf85b717453
7012	7334	7754860810086128	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32c2/cache_err_int.c.obj	5bae9607c384ba5c
7025	7346	7754860810206161	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/flash_brownout_hook.c.obj	79a5a13b04d7882
8409	8752	7754860824049053	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/cmac.c.obj	80214ea5e36a4041
8322	8653	7754860823170904	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/camellia.c.obj	cb7c567fc364b0b8
7048	7371	7754860810441428	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_generic.c.obj	6615924aa4d6c4c0
7061	7382	7754860810571439	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_issi.c.obj	dccd958e17ddc9f5
418	477	7757410777886995	bootloader-prefix/src/bootloader-stamp/bootloader-install	9065d9935ca08086
7074	7395	7754860810703661	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_mxic.c.obj	63a5d36bcaadffea
7087	7408	7754860810821909	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_gd.c.obj	99c59d5d54576288
14323	14628	7754860883186241	esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/app_trace.c.obj	c77faf1a975361d9
13400	13721	7754860873955439	esp-idf/console/CMakeFiles/__idf_console.dir/commands.c.obj	6df25dbd28952414
7099	7421	7754860810942919	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_winbond.c.obj	7fd2e6f8fd3822f2
7111	7433	7754860811069862	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_boya.c.obj	ae9cfef5bbfc880d
7148	7473	7754860811443134	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/memspi_host_driver.c.obj	1a817ab19c5d10c3
7161	7486	7754860811564465	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/cache_utils.c.obj	c8c1391a15ad4606
15293	15580	7754860892883624	esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/proto-c/wifi_ctrl.pb-c.c.obj	2c0971e640aee897
7173	7498	7754860811694505	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/flash_mmap.c.obj	8837e826b5252218
7194	7519	7754860811894508	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/flash_ops.c.obj	516ea44767d2c35
7233	7562	7754860812284515	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/esp_flash_spi_init.c.obj	69fae9f90424fcf3
8861	9220	7754860828578524	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/platform.c.obj	856fe85f745b1233
7245	7574	7754860812410009	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_os_func_app.c.obj	ecfc2ac511bcd896
14628	14944	7754860886241094	esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/src/esp_local_ctrl_handler.c.obj	23a29c2542778f5a
7283	7610	7754860812786290	esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/port/esp32c2/ext_mem_layout.c.obj	ae50279c6988a9af
7297	7622	7754860812930207	esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/esp_cache.c.obj	69d5bc6d0033e49a
9485	9555	7754860834815497	esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/library/everest.c.obj	cba0ca0314e62e73
7310	7634	7754860813057740	esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/heap_align_hw.c.obj	af1374271d52395e
7322	7648	7754860813177725	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	db190caf2bc55ed8
14072	14412	7754860880678836	esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/security/security0.c.obj	b258b0f779495a31
7433	7769	7754860814292319	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	d19cdd1942efa0a1
7359	7687	7754860813546370	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	7e679237343583ce
15169	15489	7754860891648749	esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/esp_spiffs.c.obj	45b9e4f168f2a373
7958	8283	7754860819540030	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls13_client.c.obj	1c9327233d240367
7371	7704	7754860813666371	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	bf65b9754ad12ed2
7383	7718	7754860813785939	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	8e38db6494bb289f
7396	7730	7754860813916110	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	a417fc9a2460ed17
7408	7744	7754860814036902	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	2e95194f6ad31031
7421	7756	7754860814166843	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c2.c.obj	2fb497f7d92350a0
7460	7799	7754860814554319	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c2.c.obj	e46d932218a0fc5b
7486	7827	7754860814815982	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	3c541a371f26a291
7498	7841	7754860814946002	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	ed8a86700e070bfa
8767	9125	7754860827625785	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pk_ecc.c.obj	cdf58756b5a1d8df
7549	7890	7754860815451132	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c2/esp_efuse_table.c.obj	55624c574c1a9366
9737	9830	7754860838184091	bootloader-prefix/src/bootloader-stamp/bootloader-update	b34333832106e666
7562	7903	7754860815586226	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c2/esp_efuse_fields.c.obj	4fe55a2f8e7820d0
7598	7945	7754860815936236	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	17a508aed774cba5
7610	7958	7754860816054812	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	baa1df84d2765a8
7635	7988	7754860816304811	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/without_key_purposes/one_key_block/esp_efuse_api_key.c.obj	bd9fd0702f8795a1
11874	11972	7754860858699700	esp-idf/esp_partition/libesp_partition.a	1ae04024333974f7
7648	8003	7754860816444808	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_startup.c.obj	4330f1627333d93e
9310	9549	7754860833052465	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/idf/Espressif/frameworks/esp-idf/components/mbedtls/port/esp_mem.c.obj	4bd5c58d414bfe84
7661	8017	7754860816570002	esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/partition.c.obj	3c244ce53efe565f
7674	8031	7754860816700009	esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/partition_target.c.obj	afb906c46cb7b4fd
13489	13810	7754860874850456	esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_dbl.c.obj	8f3d2f8e049bbd0c
7687	8043	7754860816830014	esp-idf/app_update/CMakeFiles/__idf_app_update.dir/esp_ota_ops.c.obj	aa0cbeece78a78fa
14578	14867	7754860885741017	esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/i2c/esp_lcd_panel_io_i2c_v1.c.obj	4e5e749d5691c49b
7704	8056	7754860817000060	esp-idf/app_update/CMakeFiles/__idf_app_update.dir/esp_ota_app_desc.c.obj	a2cbadaa322c11a7
7744	8069	7754860817404000	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/debug.c.obj	2e91079c8d079c0f
15387	15681	7754860893826622	esp-idf/boards/CMakeFiles/__idf_boards.dir/board_support/bsp_chip_id.c.obj	336d3f6b5152583
7756	8083	7754860817523994	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/mps_reader.c.obj	911da1906f2070a6
7769	8097	7754860817654008	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/mps_trace.c.obj	2dcfce25bf2c7223
8877	9235	7754860828729676	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/platform_util.c.obj	b4d8471378bd14a3
7784	8111	7754860817803437	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_cache.c.obj	81549ec59a82c1c2
6406	6653	7757410840163924	.bin_timestamp	b06e2e4cb4a58ec6
7799	8125	7754860817944783	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_ciphersuites.c.obj	14fd618662030665
7814	8140	7754860818094777	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_client.c.obj	17a94b21b2272d8b
7854	8180	7754860818503336	esp-idf/sdmmc/libsdmmc.a	738627e6a6a3522e
7862	8189	7754860818583399	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_msg.c.obj	8c0d446a44fddffe
9457	9553	7754860834534215	esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/p256-m_driver_entrypoints.c.obj	28d55aa967d4278d
7890	8216	7754860818858494	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls.c.obj	7490cffe422bb9c7
7918	8244	7754860819138977	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls12_server.c.obj	1ed25535be4bedaa
14944	15265	7754860889396083	esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/vfs/vfs_fat.c.obj	556251d66b4950ad
7931	8257	7754860819270028	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls13_keys.c.obj	4cc032df3cb968a2
7973	8295	7754860819690108	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls13_generic.c.obj	a2d57b2f16e260b1
8003	8321	7754860819994728	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/D_/idf/Espressif/frameworks/esp-idf/components/mbedtls/port/esp_platform_time.c.obj	472634b66e4bb374
8017	8334	7754860820126428	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/D_/idf/Espressif/frameworks/esp-idf/components/mbedtls/port/net_sockets.c.obj	136b4789577e4cd8
8031	8346	7754860820274256	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/pkcs7.c.obj	4d5fcf7ebd535f5b
8056	8370	7754860820520754	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509_create.c.obj	1ea2470f6c776506
8070	8382	7754860820657511	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509_crl.c.obj	5a6e2a40fcd2f67
8097	8409	7754860820928679	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509_csr.c.obj	9f22bff0ac5726ad
7718	8421	7754860817143230	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	a95048655e0d422f
8112	8432	7754860821078679	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509write.c.obj	a0252f8c00f671f3
8140	8457	7754860821357207	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509write_csr.c.obj	133848284e0ee35f
8154	8470	7754860821507207	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/aes.c.obj	d6b5e0b51485a2e1
10681	10790	7754860846769577	esp-idf/heap/libheap.a	8d8ab3751356812c
8180	8495	7754860821757214	esp-idf/esp_driver_spi/libesp_driver_spi.a	739d66a6138f6f4b
8189	8505	7754860821847209	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/aesce.c.obj	7b4bfdd237c5da8e
8653	9016	7754860826489935	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/md.c.obj	d90c156dcd3a86eb
7731	8517	7754860817268274	esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/esp_app_desc.c.obj	db28855165e83b35
8230	8561	7754860822256035	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/asn1write.c.obj	5c6a89cdf0081043
8257	8588	7754860822527502	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/bignum.c.obj	2284aec2ae6c3d6a
14603	14896	7754860885991024	esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/spi/esp_lcd_panel_io_spi.c.obj	dd8840eb99a5310b
8270	8600	7754860822667504	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/bignum_core.c.obj	f698d28d4919df43
8309	8640	7754860823054880	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/block_cipher.c.obj	fa537775a4ed87c6
8575	8931	7754860825706770	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/error.c.obj	4d2f9d5fdfdd8406
8371	8709	7754860823669038	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/cipher.c.obj	dec57440e819f3fe
8421	8766	7754860824172330	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ctr_drbg.c.obj	be1849d0d70b6935
8433	8781	7754860824282384	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/des.c.obj	86f3726094afe155
8483	8838	7754860824791280	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecjpake.c.obj	2ab45db8d38917a2
8495	8851	7754860824906238	esp-idf/nvs_flash/libnvs_flash.a	777beef4fc83f339
8600	8958	7754860825959865	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/hkdf.c.obj	e95151a332d2c9d4
8613	8972	7754860826083109	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/hmac_drbg.c.obj	f62ef47c504fc008
8985	9352	7754860829808084	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_ffdh.c.obj	298dd8c335263420
8627	8985	7754860826233201	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/lmots.c.obj	f5a58132a58c159b
13299	13608	7754860872950872	esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/src/gpio_glitch_filter_ops.c.obj	81958141dac8d046
8641	9002	7754860826364171	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/lms.c.obj	76f9635c758f6b7d
13477	13798	7754860874730452	esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_date.c.obj	b1818f2937e886c
8666	9031	7754860826620054	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/md5.c.obj	65c4acb38aad29c3
8680	9044	7754860826760088	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/memory_buffer_alloc.c.obj	942b6ffeccc17659
8694	9058	7754860826900722	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/nist_kw.c.obj	a4a2272378e760ba
8709	9071	7754860827055396	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/oid.c.obj	b133c711d8413298
8738	9098	7754860827345783	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pem.c.obj	94cf8543a552a77d
8753	9110	7754860827485812	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pk.c.obj	f7fac331dd95cf57
8781	9140	7754860827765792	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pk_wrap.c.obj	bc031ac660f799a9
8795	9154	7754860827915785	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pkcs12.c.obj	71188e514d31a307
8812	9167	7754860828077763	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pkcs5.c.obj	408fbcabe432c8f1
11519	11642	7754860855150895	esp-idf/esp_mm/libesp_mm.a	5dd7985233d39694
8838	9196	7754860828347576	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pkwrite.c.obj	978c5943f0339cb
8851	9209	7754860828473378	esp-idf/esp_event/libesp_event.a	49f6c646f287115f
8891	9250	7754860828874003	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/poly1305.c.obj	870e2ac38de6f023
8905	9266	7754860829004015	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto.c.obj	c55672f52aa66c64
14542	14837	7754860885382828	esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_panel_ssd1306.c.obj	b7a3db09d7b64047
8918	9283	7754860829144271	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_aead.c.obj	d2891dd7231cad26
8944	9310	7754860829401544	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_client.c.obj	8dfd83f955ad947a
8958	9323	7754860829542221	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_driver_wrappers_no_static.c.obj	420e547fd76bfa4a
14837	15155	7754860888322970	esp-idf/unity/libunity.a	94d546cc228f143c
9031	9396	7754860830264167	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_pake.c.obj	264db65ec2fbe60f
9044	9410	7754860830404559	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_rsa.c.obj	c137801915be039b
9058	9427	7754860830539598	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_se.c.obj	bbc6a5fc0d6b9dd5
9071	9442	7754860830673228	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_slot_management.c.obj	64e747c643d2c673
9085	9457	7754860830809343	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_storage.c.obj	904b6f326d2e07ef
9110	9485	7754860831070415	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_util.c.obj	597684cf1d1742cf
9125	9498	7754860831208015	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ripemd160.c.obj	b0d352f2aff58ab6
13870	14186	7754860878651699	esp-idf/unity/CMakeFiles/__idf_unity.dir/unity_compat.c.obj	3616ce8a147bef5b
9154	9526	7754860831506833	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/rsa_alt_helpers.c.obj	1a3e6d06be679937
13524	13849	7754860875204670	esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_file.c.obj	580f9c0418b53e7c
9167	9535	7754860831626936	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/sha1.c.obj	9111ccaadc7d59a3
13228	13524	7754860872242254	esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/esp_crt_bundle/esp_crt_bundle.c.obj	b0891fa86b4a4d57
9196	9536	7754860831919810	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/sha512.c.obj	96a3f0c5e47b335d
9209	9536	7754860832049843	esp-idf/esp_driver_uart/libesp_driver_uart.a	2ef9ff2121098fe
9220	9546	7754860832161557	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/sha3.c.obj	db6b9476c8c4d186
9250	9547	7754860832452453	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/timing.c.obj	582f9ce403e4b822
129	259	7757410774992693	esp-idf/main/CMakeFiles/__idf_main.dir/charge_system/pixel_effect/system/state_system.c.obj	94a062712febb230
9323	9549	7754860833197391	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/idf/Espressif/frameworks/esp-idf/components/mbedtls/port/esp_timing.c.obj	81f0a47898464eec
13947	14267	7754860879423002	esp-idf/unity/CMakeFiles/__idf_unity.dir/port/esp/unity_utils_memory_esp.c.obj	c77d0d65f3ae9efc
9338	9550	7754860833337905	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/idf/Espressif/frameworks/esp-idf/components/mbedtls/port/sha/esp_sha.c.obj	ec2ba65deede66a1
9367	9550	7754860833634649	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/idf/Espressif/frameworks/esp-idf/components/mbedtls/port/sha/block/esp_sha1.c.obj	b7bd161fcb6e9e17
9396	9551	7754860833924211	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/idf/Espressif/frameworks/esp-idf/components/mbedtls/port/sha/block/esp_sha512.c.obj	602b6f0a81dd53b3
9410	9551	7754860834064220	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/idf/Espressif/frameworks/esp-idf/components/mbedtls/port/ecc/esp_ecc.c.obj	38e4d26de2ea924d
9427	9552	7754860834234200	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/idf/Espressif/frameworks/esp-idf/components/mbedtls/port/ecc/ecc_alt.c.obj	3a1a8bbfc70a8ab5
9443	9553	7754860834384194	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/D_/idf/Espressif/frameworks/esp-idf/components/mbedtls/port/md/esp_md.c.obj	7dc280b3e1134df0
9472	9554	7754860834684209	esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/p256-m/p256-m.c.obj	ee44fe497e0e19b2
9636	9736	7754860837253321	bootloader-prefix/src/bootloader-stamp/bootloader-download	d99b2ff67b89a1df
9648	9751	7754860836436299	esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a	50aa0422fcf47bf9
9752	9855	7754860837471646	esp-idf/esp_timer/libesp_timer.a	92e4c659ef527ad5
9855	9972	7754860838510276	esp-idf/cxx/libcxx.a	7617d0910d108760
10072	10191	7754860840678819	esp-idf/newlib/libnewlib.a	e22486f47acf85dc
10191	10321	7754860841871635	esp-idf/freertos/libfreertos.a	57ee0258aa961cce
10321	10488	7754860843168787	esp-idf/esp_hw_support/libesp_hw_support.a	2684c381e4349064
10489	10575	7754860844845868	esp-idf/esp_security/libesp_security.a	8452bf3e008bd6ce
11162	11253	7754860851575192	esp-idf/esp_common/libesp_common.a	39b8f3ba75e35763
11254	11388	7754860852493118	esp-idf/esp_system/libesp_system.a	e88fcf1176e73cdf
11770	11874	7754860857665964	esp-idf/efuse/libefuse.a	a74c87a633e22b60
11973	12063	7754860859689872	esp-idf/app_update/libapp_update.a	cfbf1d33bd247f93
15339	15628	7754860893343280	esp-idf/protocol/libprotocol.a	9fa35fdae51b637e
12063	12161	7754860860588869	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	4f2397a06ea3129b
6052	6405	7757410834230023	charge_base.elf	c77768a98321cf1b
12369	12476	7754860863652014	esp-idf/mbedtls/mbedtls/library/libmbedx509.a	522ff3c0faf5c951
12932	13074	7754860870521085	esp-idf/mbedtls/x509_crt_bundle	e9015ebd4b56b421
12932	13074	7754860870521085	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/esp-idf/mbedtls/x509_crt_bundle	e9015ebd4b56b421
13074	13228	7754860872167063	x509_crt_bundle.S	afb1f9de0681ee80
13252	13544	7754860872477950	esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/pm_locks.c.obj	91de6c8a581737ad
13276	13580	7754860872727948	esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/pm_impl.c.obj	429227c4e0f8fb64
13288	13590	7754860872840764	esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/src/gpio.c.obj	4c15e2b2a0cf9347
14226	14555	7754860882220399	esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/wear_levelling.cpp.obj	17a41f68ca0834ee
13323	13633	7754860873195861	esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/src/dedic_gpio.c.obj	7be8d7e57123e675
13344	13659	7754860873397243	esp-idf/riscv/CMakeFiles/__idf_riscv.dir/instruction_decode.c.obj	2f589077b45b0737
13390	13709	7754860873865447	esp-idf/riscv/CMakeFiles/__idf_riscv.dir/vectors_intc.S.obj	fea2c8ca55aa1e45
13409	13734	7754860874057986	esp-idf/console/CMakeFiles/__idf_console.dir/esp_console_common.c.obj	c5f2f7c2045fe713
13451	13773	7754860874470377	esp-idf/console/CMakeFiles/__idf_console.dir/esp_console_repl_chip.c.obj	ec53789f50c864a6
13463	13785	7754860874590456	esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_cmd.c.obj	809e7e1391f69355
13544	13870	7754860875391878	esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_hashtable.c.obj	aa7450aeb046be10
13590	13910	7754860875867979	esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_rem.c.obj	a9d3406b0af9903d
14971	15292	7754860889676158	esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/vfs/vfs_fat_spiflash.c.obj	edd869525e4b00e1
13608	13923	7754860876039907	esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_rex.c.obj	234afcb58bf19153
13621	13935	7754860876174977	esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_str.c.obj	9313a5e075ea0c4a
13634	13947	7754860876295046	esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_utils.c.obj	a37370d541f8ea8b
13647	13960	7754860876435119	esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/argtable3.c.obj	3ebd72624cb3982e
14009	14338	7754860880053224	esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/proto-c/sec1.pb-c.c.obj	5682e52b34005427
13671	13983	7754860876675943	esp-idf/tools/CMakeFiles/__idf_tools.dir/check_sum/check_sum.c.obj	be6fadec94d84344
13684	13996	7754860876801087	esp-idf/tools/CMakeFiles/__idf_tools.dir/check_sum/crc.c.obj	12a652b1ddcb5c01
13734	14048	7754860877297413	esp-idf/tools/CMakeFiles/__idf_tools.dir/key_detect/key_detect.c.obj	df52c4883effd79f
13760	14072	7754860877567413	esp-idf/tools/CMakeFiles/__idf_tools.dir/fsm/fsm.c.obj	4f68c278e259cf29
13773	14084	7754860877687427	esp-idf/tools/CMakeFiles/__idf_tools.dir/str_function/str_function.c.obj	14c761e355e8880
13785	14098	7754860877807497	esp-idf/tools/CMakeFiles/__idf_tools.dir/encrypt/encrypt_aes_cbc.c.obj	8bf79b45fde6a98c
13810	14125	7754860878057416	esp-idf/tools/CMakeFiles/__idf_tools.dir/color_spcae/rgb_to_hsv.c.obj	bc604c53817d451c
13862	14177	7754860878581690	esp-idf/esp_pm/libesp_pm.a	ed937eb8f86186ea
13884	14198	7754860878793020	esp-idf/unity/CMakeFiles/__idf_unity.dir/unity_runner.c.obj	47639a44db67d348
13898	14211	7754860878942931	esp-idf/unity/CMakeFiles/__idf_unity.dir/unity_utils_freertos.c.obj	cf93ce2af88b53dd
13960	14283	7754860879552921	esp-idf/esp_https_server/CMakeFiles/__idf_esp_https_server.dir/src/https_server.c.obj	9dc6b1508e38df13
13984	14308	7754860879798125	esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/proto-c/constants.pb-c.c.obj	65217758f26a9508
14036	14368	7754860880316386	esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/proto-c/session.pb-c.c.obj	34d6f45c6d7132f6
14060	14398	7754860880562632	esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/transports/protocomm_httpd.c.obj	d0704e111a9d35c6
14098	14440	7754860880938873	esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/security/security2.c.obj	3c219d90aaf0d94d
144	259	7757410775142778	esp-idf/main/CMakeFiles/__idf_main.dir/charge_system/pixel_effect/common/pixel_common.c.obj	8dc01daa956fb1f
14153	14489	7754860881490538	esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/SPI_Flash.cpp.obj	f6d75b63c2eecd22
14177	14514	7754860881726617	esp-idf/esp_driver_gpio/libesp_driver_gpio.a	7b5f00bfc75c738f
14198	14531	7754860881940412	esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/WL_Flash.cpp.obj	9b82568a93aa00f8
15427	15727	7754860894226626	esp-idf/boards/CMakeFiles/__idf_boards.dir/board_support/bsp_pixel_led_onboard.c.obj	54b03fb61cbc4d45
68	621	7756488638196598	esp-idf/drivers/CMakeFiles/__idf_drivers.dir/husb238/drv_husb238.c.obj	12d88452d5d8231b
14309	14615	7754860883046250	esp-idf/protocol/CMakeFiles/__idf_protocol.dir/protocol_tlvc/protocol_tlvc_encrypt.c.obj	f476f2a385182057
14354	14653	7754860883502107	esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/host_file_io.c.obj	702b70839f2f673e
14384	14678	7754860883800890	esp-idf/cmock/CMakeFiles/__idf_cmock.dir/CMock/src/cmock.c.obj	fbfb0250089e67d8
14399	14690	7754860883940898	esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/esp_cam_ctlr.c.obj	fc566a0714ca3113
14413	14703	7754860884080882	esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/dvp_share_ctrl.c.obj	e2016166beb2697f
14425	14715	7754860884207380	esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/src/esp_eth.c.obj	b34a687c642ecc67
14440	14728	7754860884357462	esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/src/phy/esp_eth_phy_802_3.c.obj	9aaa4aa5d604a2f7
14465	14753	7754860884612496	esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/src/esp_hidd.c.obj	16adcc040514901c
98	258	7757410774682678	esp-idf/main/CMakeFiles/__idf_main.dir/charge_system/pixel_effect/pixeleff_manager.c.obj	8b36578b587ce953
14489	14781	7754860884855061	esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/src/esp_hid_common.c.obj	b2eab70f4d67f82d
14502	14797	7754860884984998	esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_common.c.obj	eaa92a5c7ab2a518
14514	14811	7754860885098740	esp-idf/riscv/libriscv.a	2337d54990d9a916
14555	14846	7754860885511350	esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_panel_st7789.c.obj	3ed38da7fa9b4264
14653	14971	7754860886491027	esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/src/esp_local_ctrl_transport_httpd.c.obj	838da82b24d78169
14665	14984	7754860886611011	esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_init.c.obj	a3d49e200dcdf8f0
14690	15008	7754860886861093	esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_flash.c.obj	c734b9b60952583
14728	15048	7754860887244109	esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_binary.c.obj	61380cdb4ca302f5
14798	15110	7754860887932966	esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/diskio/diskio_rawflash.c.obj	372a3c3efd23bf1d
14811	15123	7754860888072988	esp-idf/console/libconsole.a	e734b31e99c4dad0
14820	15135	7754860888152982	esp-idf/protobuf-c/libprotobuf-c.a	c7a103daaf5d5a59
14828	15143	7754860888242972	esp-idf/tools/libtools.a	4b30614ccfa36a51
14856	15182	7754860888519411	esp-idf/wear_levelling/libwear_levelling.a	42184cc6aba72ca3
14957	15279	7754860889536090	esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/vfs/vfs_fat_sdmmc.c.obj	166988b306b3322d
14994	15317	7754860889897104	esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/esp-mqtt/mqtt_client.c.obj	8a9dcca2ab36a14b
15022	15338	7754860890187143	esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/esp-mqtt/lib/mqtt_outbox.c.obj	f55bcfc4c38281ed
15035	15347	7754860890318448	esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/esp-mqtt/lib/platform_esp32_idf.c.obj	b1b294bedfea2510
15556	15794	7754860895510054	esp-idf/esp_lcd/libesp_lcd.a	25e0e36299b13a48
15060	15374	7754860890563660	esp-idf/rt/CMakeFiles/__idf_rt.dir/FreeRTOS_POSIX_mqueue.c.obj	463a22b3ae5a9481
15098	15414	7754860890942204	esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/spiffs/src/spiffs_cache.c.obj	99f3116f438701b7
15110	15427	7754860891068285	esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/spiffs/src/spiffs_check.c.obj	b2a2c91447858748
15123	15439	7754860891185979	esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/spiffs/src/spiffs_gc.c.obj	d22de1f7f6ce8c09
15135	15451	7754860891306049	esp-idf/protocomm/libprotocomm.a	62df493eeeb2525c
15182	15504	7754860891778848	esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/wifi_config.c.obj	c8cc4914d22642a8
15196	15516	7754860891918745	esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/wifi_scan.c.obj	246d4cc0d8c8cc25
15221	15533	7754860892163759	esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/manager.c.obj	83581f3e1aa15ae1
15252	15546	7754860892473646	esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/scheme_console.c.obj	d0dc3ee394e75809
15266	15556	7754860892613629	esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/proto-c/wifi_config.pb-c.c.obj	1a03d656191e6bda
15305	15591	7754860893008722	esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/proto-c/wifi_constants.pb-c.c.obj	ec11eee58572fe30
15317	15603	7754860893138719	esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/scheme_softap.c.obj	c3e52b874937eec7
621	721	7756488643729064	esp-idf/drivers/libdrivers.a	bb2f8a8544631964
15347	15640	7754860893426580	esp-idf/boards/CMakeFiles/__idf_boards.dir/board_support/bsp_timestamp.c.obj	c9a8aca143afa9d3
15374	15663	7754860893696618	esp-idf/boards/CMakeFiles/__idf_boards.dir/board_support/bsp_uart0_commander.c.obj	9122a1b6d94ba825
15439	15743	7754860894356616	esp-idf/boards/CMakeFiles/__idf_boards.dir/board_support/bsp_i2c0_bus.c.obj	85e32149de5560ad
15476	15769	7754860894723274	esp-idf/boards/CMakeFiles/__idf_boards.dir/peripheral/local_device.c.obj	2df24085341c7ec5
15533	15793	7754860895293273	esp-idf/esp_driver_cam/libesp_driver_cam.a	59886caea8dad7e3
15546	15794	7754860895420009	esp-idf/esp_hid/libesp_hid.a	23017a03e1e16430
15592	15794	7754860895875471	esp-idf/fatfs/libfatfs.a	23fc371fee7f6995
15604	15794	7754860895995465	esp-idf/mqtt/libmqtt.a	ba81c810dd7189c9
15615	15794	7754860896105466	esp-idf/nvs_sec_provider/libnvs_sec_provider.a	3086a4029e199447
231	584	7757410776009346	esp-idf/main/CMakeFiles/__idf_main.dir/charge_system/charge_manager/cg_state_slow.c.obj	624f3b4c01057000
15628	15794	7754860896240949	esp-idf/rt/librt.a	53ed5fb41c2bb18f
174	260	7757410775442689	esp-idf/main/CMakeFiles/__idf_main.dir/charge_system/charge_manager/charge_manager.c.obj	a959f82b23cc2e9d
113	258	7757410774832687	esp-idf/main/CMakeFiles/__idf_main.dir/charge_system/pixel_effect/system/pixeleff_system.c.obj	a04e72d7e9943959
15451	15798	7754860894466684	esp-idf/boards/CMakeFiles/__idf_boards.dir/peripheral/i2c_device.c.obj	9e802c2fd151213
15785	15880	7754860897810991	esp-idf/service/libservice.a	53ba959e64d3f7c0
9937	18650	7754860926381872	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure	6dd5de3dcaaa64b3
244	418	7757410776149356	bootloader/bootloader.bin	2ee90b3d3529ba39
244	418	7757410776149356	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-build	2ee90b3d3529ba39
244	418	7757410776149356	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader/bootloader.bin	2ee90b3d3529ba39
477	618	7757410779817225	bootloader-prefix/src/bootloader-stamp/bootloader-done	bdc918f166c60ce3
1067	6022	7757410833671659	esp-idf/esp_system/ld/sections.ld	1435351930cc28c0
1067	6022	7757410833671659	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/esp-idf/esp_system/ld/sections.ld	1435351930cc28c0
6022	6052	7757410833921515	CMakeFiles/charge_base.elf.dir/project_elf_src_esp32c2.c.obj	b52d3e5d6cc9dae0
24	8418	7757414041102845	build.ninja	fc03587fd325b4ae
201	558	7757410775714219	esp-idf/main/CMakeFiles/__idf_main.dir/charge_system/charge_manager/cg_state_full.c.obj	99cdb6bc3d4006f0
30	8427	7757414041102845	build.ninja	fc03587fd325b4ae
148	322	7757414043140048	bootloader-prefix/src/bootloader-stamp/bootloader-build	2ee90b3d3529ba39
148	322	7757414043140048	bootloader/bootloader.elf	2ee90b3d3529ba39
148	322	7757414043140048	bootloader/bootloader.bin	2ee90b3d3529ba39
148	322	7757414043140048	bootloader/bootloader.map	2ee90b3d3529ba39
148	322	7757414043140048	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-build	2ee90b3d3529ba39
148	322	7757414043140048	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader/bootloader.elf	2ee90b3d3529ba39
148	322	7757414043140048	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader/bootloader.bin	2ee90b3d3529ba39
148	322	7757414043140048	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader/bootloader.map	2ee90b3d3529ba39
322	381	7757414044867180	bootloader-prefix/src/bootloader-stamp/bootloader-install	9065d9935ca08086
322	381	7757414044867180	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-install	9065d9935ca08086
381	529	7757414046868657	CMakeFiles/bootloader-complete	bdc918f166c60ce3
381	529	7757414046868657	bootloader-prefix/src/bootloader-stamp/bootloader-done	bdc918f166c60ce3
381	529	7757414046868657	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/CMakeFiles/bootloader-complete	bdc918f166c60ce3
381	529	7757414046868657	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-done	bdc918f166c60ce3
92	812	7757414042567937	esp-idf/main/CMakeFiles/__idf_main.dir/charge_system/charge_manager/cg_state_charging.c.obj	20f76031798cce42
111	818	7757414042747938	esp-idf/main/CMakeFiles/__idf_main.dir/charge_system/charge_manager/cg_state_full.c.obj	99cdb6bc3d4006f0
60	1039	7757414042244171	esp-idf/main/CMakeFiles/__idf_main.dir/charge_system/charge_system.c.obj	59b62edcaf257a4e
124	1048	7757414042880048	esp-idf/main/CMakeFiles/__idf_main.dir/charge_system/charge_manager/cg_state_idle.c.obj	b23fc17ab04824b6
70	226	7757414155742118	bootloader-prefix/src/bootloader-stamp/bootloader-build	2ee90b3d3529ba39
70	226	7757414155742118	bootloader/bootloader.elf	2ee90b3d3529ba39
70	226	7757414155742118	bootloader/bootloader.bin	2ee90b3d3529ba39
70	226	7757414155742118	bootloader/bootloader.map	2ee90b3d3529ba39
70	226	7757414155742118	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-build	2ee90b3d3529ba39
70	226	7757414155742118	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader/bootloader.elf	2ee90b3d3529ba39
70	226	7757414155742118	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader/bootloader.bin	2ee90b3d3529ba39
70	226	7757414155742118	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader/bootloader.map	2ee90b3d3529ba39
227	280	7757414157316096	bootloader-prefix/src/bootloader-stamp/bootloader-install	9065d9935ca08086
227	280	7757414157316096	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-install	9065d9935ca08086
280	420	7757414159180808	CMakeFiles/bootloader-complete	bdc918f166c60ce3
280	420	7757414159180808	bootloader-prefix/src/bootloader-stamp/bootloader-done	bdc918f166c60ce3
280	420	7757414159180808	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/CMakeFiles/bootloader-complete	bdc918f166c60ce3
280	420	7757414159180808	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/bootloader-prefix/src/bootloader-stamp/bootloader-done	bdc918f166c60ce3
57	676	7757414155623680	esp-idf/main/CMakeFiles/__idf_main.dir/charge_system/charge_manager/charge_manager.c.obj	a959f82b23cc2e9d
676	781	7757414161800812	esp-idf/main/libmain.a	1daccb56ec796534
781	5583	7757414210598103	esp-idf/esp_system/ld/sections.ld	1435351930cc28c0
781	5583	7757414210598103	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/esp-idf/esp_system/ld/sections.ld	1435351930cc28c0
5583	5925	7757414210868853	charge_base.elf	c77768a98321cf1b
5926	6169	7757414216671356	.bin_timestamp	b06e2e4cb4a58ec6
5926	6169	7757414216671356	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/.bin_timestamp	b06e2e4cb4a58ec6
6169	6239	7757414216737318	esp-idf/esptool_py/CMakeFiles/app_check_size	b7331fda4f4225c0
6169	6239	7757414216737318	D:/idf/ebed_framework_esp32/proj_charge_base_esp32c2/build/esp-idf/esptool_py/CMakeFiles/app_check_size	b7331fda4f4225c0
