#include "driver/gpio.h"
#include "driver/uart.h"

#include "logger.h"

#define ECHO_TEST_TXD  (UART_PIN_NO_CHANGE)
#define ECHO_TEST_RXD  (UART_PIN_NO_CHANGE)
#define ECHO_TEST_RTS  (UART_PIN_NO_CHANGE)
#define ECHO_TEST_CTS  (UART_PIN_NO_CHANGE)

#define BUF_SIZE (1024)

#define tag "[BSP]uart0"

int32_t bsp_uart0_commander_init()
{
    uart_config_t uart_config = {
        .baud_rate = 115200,
        .data_bits = UART_DATA_8_BITS,
        .parity    = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .source_clk = UART_SCLK_DEFAULT,
    };

    if (uart_driver_install(UART_NUM_0, BUF_SIZE * 2, 0, 0, NULL, 0) != ESP_OK)
    {
        mlog_e(tag, "driver install failed");
        return -1;
    }
    if (uart_param_config(UART_NUM_0, &uart_config) != ESP_OK)
    {
        mlog_e(tag, "param config failed");
        return -2;
    }
    if (uart_set_pin(UART_NUM_0, ECHO_TEST_TXD, ECHO_TEST_RXD, ECHO_TEST_RTS, ECHO_TEST_CTS) != ESP_OK)
    {
        mlog_e(tag, "set pin failed");
        return -3;
    }
    return 0;
}

int32_t bsp_uart0_commander_receive(uint8_t *data, uint32_t buf_len)
{
    return uart_read_bytes(UART_NUM_0, data, buf_len, 1 / portTICK_RATE_MS);
}

int32_t bsp_uart0_commander_transmit(uint8_t *data, uint32_t len)
{
    return uart_write_bytes(UART_NUM_0, (const char *) data, len);
}
