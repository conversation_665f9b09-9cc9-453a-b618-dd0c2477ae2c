#include "driver/gpio.h"
#include "board_define.h"
#include "logger.h"
#include "esp_adc/adc_oneshot.h"
#include "esp_adc/adc_cali.h"
#include "esp_adc/adc_cali_scheme.h"

#define tag "[BSP]gpio det"

#define GPIO_V_DE           GPIO_NUM_2

#define ADC1_CHAN0          ADC_CHANNEL_0
#define ADC1_CHAN1          ADC_CHANNEL_1
#define ADC1_CHAN2          ADC_CHANNEL_2
#define ADC1_CHAN3          ADC_CHANNEL_3
#define ADC1_CHAN4          ADC_CHANNEL_4
#define ADC1_CHAN5          ADC_CHANNEL_5
#define ADC1_CHAN6          ADC_CHANNEL_6
#define ADC1_CHAN7          ADC_CHANNEL_7

#define ADC_ATTEN           ADC_ATTEN_DB_12
adc_oneshot_unit_handle_t adc1_handle;
adc_cali_handle_t adc1_cali_chan0_handle = NULL;
static uint8_t do_calibration1_chan[8] = {0};

static int adc_raw[8];
static int voltage[8];

static bool example_adc_calibration_init(adc_unit_t unit, adc_channel_t channel, adc_atten_t atten, adc_cali_handle_t *out_handle);

int32_t bsp_adc_init(void)
{
    //-------------ADC1 Init---------------//
    adc1_handle = calloc(1, sizeof(adc_oneshot_unit_handle_t));
    adc1_cali_chan0_handle = calloc(1, sizeof(adc_cali_handle_t));
    adc_oneshot_unit_init_cfg_t init_config1 = {
        .unit_id = ADC_UNIT_1,
    };
    // gpio_set_pull_mode(GPIO_NUM_2, GPIO_PULLUP_ONLY);
    // gpio_set_pull_mode(GPIO_NUM_5, GPIO_PULLUP_ONLY);

    ESP_ERROR_CHECK(adc_oneshot_new_unit(&init_config1, &adc1_handle));

        //-------------ADC1 Config---------------//
    adc_oneshot_chan_cfg_t config = {
        .bitwidth = ADC_BITWIDTH_DEFAULT,
        .atten = ADC_ATTEN,
    };

    ESP_ERROR_CHECK(adc_oneshot_config_channel(adc1_handle, ADC1_CHAN2, &config));
    //-------------ADC1 Calibration Init---------------//
    
    do_calibration1_chan[ADC1_CHAN2] = example_adc_calibration_init(ADC_UNIT_1, ADC1_CHAN2, ADC_ATTEN, &adc1_cali_chan0_handle);
    // for(uint8_t i = 0; i < 8; i++)
    // {
    //     do_calibration1_chan[i] =  example_adc_calibration_init(ADC_UNIT_1, i, ADC_ATTEN, &adc1_cali_chan0_handle);
    // }

    return 0;
}

/*ADC_CHANNEL_0~ADC_CHANNEL_7;0~7
return mv*/
int32_t bsp_adc_get(uint8_t channel)
{
    if(channel > ADC1_CHAN7){
        return -1;
    }
    ESP_ERROR_CHECK(adc_oneshot_read(adc1_handle, channel, &adc_raw[channel]));
    // ESP_LOGI(tag, "ADC%d Channel[%d] Raw Data: %d", ADC_UNIT_1 + 1, channel, adc_raw[channel]);
    if (do_calibration1_chan[channel])
    {
        ESP_ERROR_CHECK(adc_cali_raw_to_voltage(adc1_cali_chan0_handle, adc_raw[channel], &voltage[channel]));
        // ESP_LOGI(tag, "ADC%d Channel[%d] Cali Voltage: %d mV", ADC_UNIT_1 + 1, channel, voltage[channel]);
    }
    // adc value to voltage
    
    return voltage[channel];
}

/*---------------------------------------------------------------
        ADC Calibration
---------------------------------------------------------------*/
static bool example_adc_calibration_init(adc_unit_t unit, adc_channel_t channel, adc_atten_t atten, adc_cali_handle_t *out_handle)
{
    adc_cali_handle_t handle = NULL;
    esp_err_t ret = ESP_FAIL;
    bool calibrated = false;

#if ADC_CALI_SCHEME_CURVE_FITTING_SUPPORTED
    if (!calibrated) {
        ESP_LOGI(tag, "calibration scheme version is %s", "Curve Fitting");
        adc_cali_curve_fitting_config_t cali_config = {
            .unit_id = unit,
            .chan = channel,
            .atten = atten,
            .bitwidth = ADC_BITWIDTH_DEFAULT,
        };
        ret = adc_cali_create_scheme_curve_fitting(&cali_config, &handle);
        if (ret == ESP_OK) {
            calibrated = true;
        }
    }
#endif

#if ADC_CALI_SCHEME_LINE_FITTING_SUPPORTED
    if (!calibrated) {
        ESP_LOGI(tag, "calibration scheme version is %s", "Line Fitting");
        adc_cali_line_fitting_config_t cali_config = {
            .unit_id = unit,
            .atten = atten,
            .bitwidth = ADC_BITWIDTH_DEFAULT,
        };
        ret = adc_cali_create_scheme_line_fitting(&cali_config, &handle);
        if (ret == ESP_OK) {
            calibrated = true;
        }
    }
#endif

    *out_handle = handle;
    if (ret == ESP_OK) {
        ESP_LOGI(tag, "Calibration Success");
    } else if (ret == ESP_ERR_NOT_SUPPORTED || !calibrated) {
        ESP_LOGW(tag, "eFuse not burnt, skip software calibration");
    } else {
        ESP_LOGE(tag, "Invalid arg or no memory");
    }

    return calibrated;
}

int32_t bsp_gpio_v_de_state(uint8_t k)
{
    int32_t state = bsp_adc_get(GPIO_V_DE);
    return state;
}
