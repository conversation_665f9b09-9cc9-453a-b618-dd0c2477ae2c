#include "esp_system.h"
#include "esp_mac.h"
#include "logger.h"
#include <string.h>

#define tag "[BSP]chip id"

#define CHIP_ID_LEN     (6)

static uint8_t chip_id[CHIP_ID_LEN];

int32_t bsp_chip_id_init()
{
    esp_err_t ret = esp_efuse_mac_get_default(chip_id);
    if (ret != ESP_OK)
    {
        mlog_e(tag, "chip id, init failed");
        return -1;
    }
    return 0;
}

int32_t bsp_chip_id_get(uint8_t *chipid, int32_t *len)
{
    int32_t in_len = *len;
    if (in_len < CHIP_ID_LEN)
    {
        mlog_e(tag, "get failed, input buffer len < CHIP_ID_LEN, %d/%d",
                in_len, CHIP_ID_LEN);
        return -1;
    }
    memcpy(chipid, chip_id, CHIP_ID_LEN);
    *len = CHIP_ID_LEN;
    return 0;
}