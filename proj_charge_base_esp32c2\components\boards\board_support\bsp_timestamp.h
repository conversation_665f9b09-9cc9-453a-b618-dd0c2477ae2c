#ifndef _BSP_TIMESTAMP_H_
#define _BSP_TIMESTAMP_H_

#include <stdint.h>

#ifdef __cplusplus
 extern "C" {
#endif

int32_t bsp_timestamp_init(void);

uint64_t bsp_get_micros(void);

uint32_t bsp_get_millis(void);

float bsp_get_second(void);

void bsp_block_delayus(uint32_t us);

void bsp_block_delayms(uint32_t ms);

void bsp_rtos_delayms(uint32_t ms);

int32_t bsp_timestamp_set_log(uint8_t sta, uint8_t timems);

#ifdef __cplusplus
 }
#endif

#endif



