# Edit following two lines to set component requirements (see docs)

set(COMPONENT_REQUIRES esp_timer mbedtls esp_https_ota)

list(APPEND COMPONENT_ADD_INCLUDEDIRS .
									  ./logger
									  ./check_sum
									  ./hash_channel
									  ./key_detect
									  ./pad_trigger
									  ./fsm
									  ./hash_search
									  ./str_function
									  ./uthash-master/src
									  ./encrypt
									  ./encrypt/encrypt_keys
									  ./color_spcae
									  ./generate_iv
									  ./pcm_convert)
set(COMPONENT_SRCS
.
./check_sum/check_sum.c
./check_sum/crc.c
./hash_channel/channel_pool.c
./hash_channel/channel_table.c
./hash_search/hash_search.c
./key_detect/key_detect.c
./pad_trigger/pad_trigger.c
./fsm/fsm.c
./str_function/str_function.c
./encrypt/encrypt_aes_cbc.c
./color_spcae/hsv_to_rgb.c
./color_spcae/rgb_to_hsv.c
./generate_iv/generate_iv.c
./pcm_convert/pcm_convert.c
)

register_component()

