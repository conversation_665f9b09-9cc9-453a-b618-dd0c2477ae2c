#ifndef __HARDWARE_LINK_H__
#define __HARDWARE_LINK_H__


#include "stdint.h"
#include "board_define.h"
#include "type_timestamp.h"
#include "type_nvs_storage.h"
#include "type_comport.h"
#include "type_chip.h"
#include "type_pixel_led.h"
#include "type_gpio_ctrl.h"
#include "type_adc.h"


#ifdef __cplusplus
extern "C" {
#endif

int32_t board_drv_link_chip_id(dev_chip_id_t *dev);
int32_t board_drv_link_timestamp(dev_timestamp_t *dev);
int32_t board_drv_link_nvs(dev_nvs_t *dev);
int32_t board_drv_link_comport0(dev_comport_t *dev);
int32_t board_drv_link_i2c0_bus(dev_i2c_bus_t *dev);
int32_t board_drv_link_vbus_ctrl(dev_gpio_ctrl_t *dev);
int32_t board_drv_link_pixel_onboard(dev_pixel_led_t *dev);
int32_t board_drv_link_adc(dev_adc_t *dev);

#ifdef __cplusplus
}
#endif


#endif

