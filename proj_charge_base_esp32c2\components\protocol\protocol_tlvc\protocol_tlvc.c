#include "protocol_tlvc.h"
#include <string.h>
#include <stdlib.h>
#include "logger.h"
#include "crc.h"

#define	TAG	 "tlvc"

// 
// return : offset
// 

static uint32_t decode_switch(protocol_tlvc_t *tlvc, uint32_t offset, uint8_t *data, uint32_t len)
{
	uint32_t used_bytes = 1;
	//mlog_i("tlvc", "state:%c", tlvc->state);
	switch (tlvc->state)
	{	
	case '0': // head0
		{
			if (data[offset] == tlvc->head0)
			{
				tlvc->state = '1';				
			}			
		} break;			
	case '1': // head1
		{
			if (data[offset] == tlvc->head1)
			{
				tlvc->state = 'L';
				tlvc->index= 0 ;
			}
			else
			{
				tlvc->state = '0';	
			}
		} break;			
	case 'L': // length
		{
			if(tlvc->index == 0)
			{
				tlvc->len_l = data[offset];
				tlvc->index = 1;
			}
			else if(tlvc->index == 1)
			{
				tlvc->len_h = data[offset];
				tlvc->state = '8';
			}
		} break;
	case '8': // head+length crc8
		{
			uint8_t crc8_data[4]={tlvc->head0, tlvc->head1, tlvc->len_l, tlvc->len_h};
			tlvc->crc8 = crc8(crc8_data,4);
			if (tlvc->crc8 == data[offset])
			{
				tlvc->state = 'V';	 
				tlvc->total_len = (tlvc->len_h << 8)+ tlvc->len_l;
				tlvc->value_len = tlvc->total_len - 7;
				tlvc->buffer.bytes = 0;
			}
			else
			{
				tlvc->state = '0';	
			}
		} break;		
	case 'V':  // value
		{
			uint32_t left_size = tlvc->value_len - tlvc->buffer.bytes;
			uint32_t data_size = len - offset;
			uint32_t copy_size = (left_size < data_size) ? (left_size) : (data_size);

			uint32_t buf_offset = tlvc->buffer.bytes;
			memcpy(tlvc->buffer.data + buf_offset, (data + offset), copy_size);

			tlvc->buffer.bytes += copy_size;
			if (tlvc->buffer.bytes == tlvc->value_len)
			{
				tlvc->state = 'C';		
				tlvc->index= 0 ;
			}
			else if (tlvc->buffer.bytes > tlvc->value_len)
			{
				tlvc->state = '0'; // size wrong
			}
			// !!! not 1 byte
			used_bytes = copy_size;

		} break;	
	case 'C': // CRC16 
		{						
			if(tlvc->index == 0)
			{
				tlvc->crc16 = (uint16_t)(data[offset]);
				tlvc->index = 1;
			}
			else if(tlvc->index == 1)
			{
				tlvc->crc16 |= (uint16_t)(data[offset]) << 8;
				uint16_t crc16_local = crc16(tlvc->buffer.data, tlvc->value_len);
				if (crc16_local != tlvc->crc16)
				{
					// crc16 error !
					mlog_int_e("tlvc", 100, "crc16 failed:%d-%d", crc16_local, tlvc->crc16);
				}
				else
				{
					for (uint32_t i = 0; i < tlvc->elem_num; i++)
					{
						if ((tlvc->elems[i].taglen == 1) 
						 && (tlvc->buffer.data[0] == 1)
						 && (tlvc->elems[i].tag[0] == tlvc->buffer.data[1]))
						{
							uint8_t *val_ptr = tlvc->buffer.data + tlvc->elems[i].taglen + 1;
							uint32_t val_len = tlvc->buffer.bytes - tlvc->elems[i].taglen - 1;
							tlvc->elems[i].pfunc(val_ptr, val_len);
						}
						else
						{
							//mlog_i("", "[%u][%s]", tlvc->elems[i].taglen, tlvc->elems[i].tag);
							if (strncmp(tlvc->elems[i].tag,
									(const char*)(tlvc->buffer.data + 1),
									tlvc->elems[i].taglen) == 0)
							{
								uint8_t *val_ptr = tlvc->buffer.data + tlvc->elems[i].taglen + 1;
								uint32_t val_len = tlvc->buffer.bytes - tlvc->elems[i].taglen - 1;
								tlvc->elems[i].pfunc(val_ptr, val_len);
								break;
							}
						}
					}
				}
				tlvc->state = '0';
			}
		} break;					
	default :break;
	}

	return (offset + used_bytes);
}

static int32_t protocol_tlvc_put_byte(protocol_tlvc_t *tlvc, uint8_t byte)
{
	uint8_t flag_tag = 0;
	uint32_t offset = 5;

	switch (tlvc->state)
	{	
		case '0': // head0
			{
				if (byte == tlvc->head0)
				{
					tlvc->state = '1';				
				}			
			} break;			
		case '1': // head1
			{
				if (byte == tlvc->head1)
				{
					tlvc->state = 'L';
					tlvc->index= 0 ;
				}
				else
				{
					tlvc->state = '0';	
				}
			} break;			
		case 'L': // length
			{
				if(tlvc->index == 0)
				{
					tlvc->len_l = byte;
					tlvc->index = 1;
				}
				else if(tlvc->index == 1)
				{
					tlvc->len_h = byte;
					tlvc->state = '8';
				}
			} break;
		case '8': // head+length crc8
			{
				uint8_t local_crc8 = byte;
				uint8_t crc8_data[4]={tlvc->head0, tlvc->head1, tlvc->len_l, tlvc->len_h};
				tlvc->crc8 = crc8(crc8_data, 4);
				if (tlvc->crc8 == local_crc8)
				{
					tlvc->state = 'V';	 
					tlvc->total_len = (tlvc->len_h << 8)+ tlvc->len_l;
					tlvc->value_len = tlvc->total_len - 7;
					tlvc->buffer.bytes = 0;
				}
				else
				{
					tlvc->state = '0';	
				}
			} break;		
		case 'V':  // value
			{
				if (tlvc->buffer.bytes < tlvc->value_len)
                {
                    // tlvc->buffer.data[offset + tlvc->buffer.bytes] = byte;
					tlvc->buffer.bytes++;
                }
                if (tlvc->buffer.bytes == tlvc->value_len)
                {
                    tlvc->index = 0;
                    tlvc->state = 'C';
                }

			} break;	
		case 'C': // CRC16 
			{						
				if(tlvc->index == 0)
				{
					tlvc->crc16 = (uint16_t)(byte);
					tlvc->index = 1;
				}
				else if(tlvc->index == 1)
				{
					tlvc->crc16 |= (uint16_t)(byte) << 8;
					uint16_t crc16_local = crc16(tlvc->buffer.data + offset, tlvc->value_len);
					if (crc16_local != tlvc->crc16)
					{
						// crc16 error !
						mlog_int_e("tlvc", 100, "crc16 failed:%d-%d", crc16_local, tlvc->crc16);
						tlvc->state = '0';
                        break;
					}
					else
					{
						for (uint32_t i = 0; i < tlvc->elem_num; i++)
						{
							if ((tlvc->elems[i].taglen == 1) 
							&& (tlvc->buffer.data[offset] == 1)
							&& (tlvc->elems[i].tag[0] == tlvc->buffer.data[offset + 1]))
							{
								flag_tag = 1;
							}
							else
							{
								//mlog_i("", "[%u][%s]", tlvc->elems[i].taglen, tlvc->elems[i].tag);
								if (strncmp(tlvc->elems[i].tag,
										(const char*)(tlvc->buffer.data + offset + 1),
										tlvc->elems[i].taglen) == 0)
								{
									flag_tag = 1;
									
								}
							}
							if (flag_tag)
							{
								uint8_t *val_ptr = tlvc->buffer.data + offset + tlvc->elems[i].taglen + 1;
								uint32_t val_len = tlvc->buffer.bytes - tlvc->elems[i].taglen - 1;
								tlvc->elems[i].pfunc(val_ptr, val_len);
								break;
							}
						}
					}
					tlvc->state = '0';
				}
			} break;					
		default :
		     tlvc->state = '0';
		     break;
	}
	return 0;
}

int32_t protocol_tlvc_decode(protocol_tlvc_t *tlvc, uint8_t *data, uint32_t len)
{
	uint32_t offset = 0;
	do
	{
		offset = decode_switch(tlvc, offset, data, len);
		//mlog_i(TAG, "offset:%d, state:%c", offset, tlvc->state);
	} while (offset < len);
    return 0;
}

static int32_t protocol_tlvc_decoding(protocol_tlvc_t *tlvc, uint8_t *data, uint32_t len)
{
	for (int32_t i = 0; i < len; i++)
	{
		protocol_tlvc_put_byte(tlvc, data[i]);
	}
	return 0;
}

static int32_t protocol_vortex_decrypt(protocol_tlvc_t *tlvc, data_encrypt_t  *encrypt, uint8_t byte)
{
    int32_t ret = -1;

    switch (encrypt->state) 
    {
        case DECRYPT_STATE_HEADER_A:
            if (byte == ENCRYPT_HEAD_A) {
                tlvc->buffer.data[0] = byte;
                encrypt->received_length = 1;
                encrypt->state = DECRYPT_STATE_HEADER_B;
            }
            break;

        case DECRYPT_STATE_HEADER_B:
            if (byte == ENCRYPT_HEAD_B) {
                tlvc->buffer.data[1] = byte;
                encrypt->received_length = 2;
                encrypt->state = DECRYPT_STATE_IV;
            } else {
                encrypt->state = DECRYPT_STATE_HEADER_A;
            }
            break;

        case DECRYPT_STATE_IV:
            tlvc->buffer.data[encrypt->received_length++] = byte;
            if (encrypt->received_length == 2 + ENCRYPT_IV_LENGTH) {
				// extract iv
				memcpy(encrypt->decrypt_iv, &tlvc->buffer.data[2], ENCRYPT_IV_LENGTH);
                encrypt->state = DECRYPT_STATE_LENGTH_LSB;
            }
            break;

        case DECRYPT_STATE_LENGTH_LSB:
            tlvc->buffer.data[encrypt->received_length++] = byte;
            encrypt->data_length = byte;
            encrypt->state = DECRYPT_STATE_LENGTH_MSB;
            break;

        case DECRYPT_STATE_LENGTH_MSB:
            tlvc->buffer.data[encrypt->received_length++] = byte;
            encrypt->data_length |= (byte << 8);
            encrypt->state = DECRYPT_STATE_CRC8;
            break;

        case DECRYPT_STATE_CRC8:
            tlvc->buffer.data[encrypt->received_length++] = byte;
            encrypt->crc8_value = byte;
            uint8_t calculated_crc8 = crc8(tlvc->buffer.data, 2 + ENCRYPT_IV_LENGTH + 2);
            if (encrypt->crc8_value != calculated_crc8) {
                encrypt->received_length = 0;
                encrypt->data_length = 0;
                encrypt->state = DECRYPT_STATE_HEADER_A;
                return -1; // CRC8校验失败，返回错误
            }
            encrypt->state = DECRYPT_STATE_DATA;
            break;

        case DECRYPT_STATE_DATA:
            tlvc->buffer.data[encrypt->received_length++] = byte;
            if (encrypt->received_length == (encrypt->data_length - 2)) {
                encrypt->state = DECRYPT_STATE_CRC16_LSB;
            }
            break;

        case DECRYPT_STATE_CRC16_LSB:
            tlvc->buffer.data[encrypt->received_length++] = byte;
            encrypt->crc16_value = byte;
            encrypt->state = DECRYPT_STATE_CRC16_MSB;
            break;

        case DECRYPT_STATE_CRC16_MSB:
            tlvc->buffer.data[encrypt->received_length++] = byte;
            encrypt->crc16_value |= (byte << 8);
            uint16_t calculated_crc16 = crc16(tlvc->buffer.data, encrypt->received_length - 2);
            if (encrypt->crc16_value != calculated_crc16) {
                encrypt->state = DECRYPT_STATE_HEADER_A;
                return -1; // CRC16校验失败，返回错误
            }
            
            uint16_t plain_text_length = encrypt->received_length - ENCRYPT_PROTOCOL_LENGTH;
            for (uint16_t i = 0; i < plain_text_length; i++) {
                tlvc->buffer.data[i] = tlvc->buffer.data[2 + ENCRYPT_IV_LENGTH + 2 + 1 + i] ^ encrypt->key[i % ENCRYPT_KEY_LENGTH] ^ encrypt->decrypt_iv[i % ENCRYPT_IV_LENGTH];
            }
            // plain callback
            if (encrypt->decrypt_callback) {
                encrypt->decrypt_callback(tlvc, tlvc->buffer.data, (uint32_t)plain_text_length);
            }

            encrypt->state = DECRYPT_STATE_HEADER_A;
            ret = 0;
            break;

        default:
            encrypt->state = DECRYPT_STATE_HEADER_A;
            break;
    }

    return ret;
}

int32_t protocol_tlvc_decrypt(protocol_tlvc_t *tlvc, data_encrypt_t  *encrypt, uint8_t *data, uint32_t len)
{
	for (int32_t i = 0; i < len; i++)
	{
		protocol_vortex_decrypt(tlvc, encrypt, data[i]);
	}
	return 0;
}

int32_t protocol_tlvc_regist(protocol_tlvc_t *tlvc, 
							const char head0,
							const char head1,
							element_tlvc_t elems[], 
							uint32_t elemsize, 
							uint8_t* buf, 
							uint32_t buflen)
{    
	// state
	tlvc->state 	= '0';
	tlvc->head0 	= head0;
	tlvc->head1 	= head1;
	tlvc->len_h 	= 0;
	tlvc->len_l 	= 0;
	tlvc->crc8 		= 0;
	tlvc->crc16		= 0;
	tlvc->total_len	= 0;
	tlvc->value_len	= 0;

    // data buffer
    tlvc->buffer.data	= buf;
    tlvc->buffer.bytes 	= 0;
    tlvc->buffer.length = buflen;
    //mlog_i(TAG, "regist, buffer len = %d", tlvc->buffer.length);

    // elements
    tlvc->elem_num = elemsize;
    tlvc->elems    = (element_tlvc_t*)malloc(elemsize * sizeof(element_tlvc_t));
    if (tlvc->elems == NULL)
    {
        return -1;
    }

    for (uint32_t i = 0; i < tlvc->elem_num; i++)
    {
        tlvc->elems[i].tag     = elems[i].tag;
        tlvc->elems[i].taglen  = elems[i].taglen;
        tlvc->elems[i].pfunc   = elems[i].pfunc;

    }

    return 0;
}

int32_t protocol_tlvc_regist_encrypt(protocol_tlvc_t *tlvc, 
								const char head0,
								const char head1,
								element_tlvc_t elems[], 
								uint32_t elemsize, 
								uint8_t* buf, 
								uint32_t buflen,
								data_encrypt_t  *encrypt,
								uint8_t *key, 
								uint8_t keylen,
							    pfunc_encrypt_generate_iv pfunc_iv)
{    
	// state
	tlvc->state 	= '0';
	tlvc->head0 	= head0;
	tlvc->head1 	= head1;
	tlvc->len_h 	= 0;
	tlvc->len_l 	= 0;
	tlvc->crc8 		= 0;
	tlvc->crc16		= 0;
	tlvc->total_len	= 0;
	tlvc->value_len	= 0;

	// data buffer
	tlvc->buffer.data	= buf;
	tlvc->buffer.bytes 	= 0;
	tlvc->buffer.length = buflen;
	//mlog_i(TAG, "regist, buffer len = %d", tlvc->buffer.length);

	// elements
	tlvc->elem_num = elemsize;
	tlvc->elems    = (element_tlvc_t*)malloc(elemsize * sizeof(element_tlvc_t));
	if (tlvc->elems == NULL)
	{
	return -1;
	}

	for (uint32_t i = 0; i < tlvc->elem_num; i++)
	{
		tlvc->elems[i].tag     = elems[i].tag;
		tlvc->elems[i].taglen  = elems[i].taglen;
		tlvc->elems[i].pfunc   = elems[i].pfunc;

	}

	// encrypt parameter
	if (encrypt != NULL)
	{
		encrypt->key_length = 0;
		if (key != NULL && keylen == ENCRYPT_KEY_LENGTH)
		{
			encrypt->key_length = keylen;
			memcpy(encrypt->key, key, keylen);
		}
		if (pfunc_iv)
		{
			encrypt->generate_iv = pfunc_iv;
		}
		encrypt->decrypt_callback = protocol_tlvc_decoding;
		encrypt->generate_iv(encrypt->iv, &encrypt->iv_length);
	}

	return 0;
}
	 
int32_t protocol_tlvc_pack(const char head0,
							const char head1,	
							const char *tag,
							uint32_t taglen,
							uint8_t *value, 
							uint32_t valuelen,
							uint8_t *data,
							uint32_t datalen)
{	
	int32_t offset = 0;
	data[0] = head0;
	data[1] = head1;
 
	uint16_t pack_len = 0;
	if (taglen == 0)
	{
		pack_len = 5 + valuelen + 2;
	}
	else
	{
		pack_len = 5 + 1 + taglen + valuelen + 2;
	}

	memcpy(data + 2, &pack_len, 2);

	uint8_t crc8_t = crc8(&data[0], 4);
	data[4] = crc8_t;
 
 	offset = 5;

 	if (taglen > 0)
 	{
		memcpy(data + offset, &taglen, 1);
		offset += 1;
		memcpy(data + offset, tag, taglen);
		offset += taglen;
 	}

 	if (valuelen > 0)
 	{
		memcpy(data + offset, value, valuelen);
		offset += valuelen;
	}

	uint32_t crc_len = 0;
	if (taglen == 0)
	{
		crc_len = valuelen;
	}
	else
	{
		crc_len = (1 + taglen + valuelen);
	}
	uint16_t crc16_val = crc16(data + 5, crc_len);
	memcpy(data + offset, &crc16_val, 2);
	offset += 2;

	return offset;
}

int32_t protocol_tlvc_pack_encrypt(const char head0,
									const char head1,	
									const char *tag,
									uint32_t taglen,
									uint8_t *value, 
									uint32_t valuelen,
									uint8_t *data,
									uint32_t datalen,
								    data_encrypt_t  *encrypt)
{	
	int32_t i = 0;
	int32_t index = 0;
	int32_t pack_len = 0;
	int32_t encrypt_len = 0;
	uint8_t crc8_value = 0;
    uint16_t crc16_value = 0;

	pack_len = protocol_tlvc_pack(head0, head1, tag, taglen, value, valuelen, data, datalen);

	// encrypt data need offset 17bytes
	encrypt->generate_iv(encrypt->iv, &encrypt->iv_length);
	encrypt_len = pack_len + ENCRYPT_PROTOCOL_LENGTH;
	if (encrypt_len > datalen)
	{
		mlog_e("tlvc_encrypt", "data cache not enough, expect %d was %d!!\r\n", encrypt_len, datalen);
		return -1;
	}
	uint8_t encrypt_data[pack_len];

	memcpy(encrypt_data, data, pack_len);

	data[index++] = ENCRYPT_HEAD_A;
	data[index++] = ENCRYPT_HEAD_B;
	memcpy(data + 2, encrypt->iv, ENCRYPT_IV_LENGTH);
	index += ENCRYPT_IV_LENGTH;

	data[index++] = (uint8_t)(encrypt_len & 0x00FF);
    data[index++] = (uint8_t)((encrypt_len >> 8) & 0x00FF);
	crc8_value = crc8(data, index);
	data[index++] = crc8_value;
	for (i = 0; i < pack_len; i++)
	{
		data[index + i] = encrypt_data[i] ^ encrypt->key[i % ENCRYPT_KEY_LENGTH] ^ encrypt->iv[i % ENCRYPT_IV_LENGTH];
	}
	index += i;

	crc16_value = crc16(data, index);
    data[index++] = (uint8_t)(crc16_value & 0xFF);
    data[index++] = (uint8_t)((crc16_value >> 8) & 0xFF);

	return encrypt_len;
}

int32_t protocol_tlvc_regist_value(protocol_tlvc_t *tlvc, 
							const char head0,
							const char head1,
							uint8_t* buf, 
							uint32_t buflen)
{    
	// state
	tlvc->state 	= '0';
	tlvc->head0 	= head0;
	tlvc->head1 	= head1;
	tlvc->len_h 	= 0;
	tlvc->len_l 	= 0;
	tlvc->crc8 		= 0;
	tlvc->crc16		= 0;
	tlvc->total_len	= 0;
	tlvc->value_len	= 0;

    // data buffer
    tlvc->buffer.data	= buf;
    tlvc->buffer.bytes 	= 0;
    tlvc->buffer.length = buflen;
    // elements
    tlvc->elem_num = 0;
    tlvc->elems    = NULL;
    return 0;
}

void protocol_tlvc_reset(protocol_tlvc_t *tlvc)
{
	tlvc->state 	= '0';
	tlvc->len_h 	= 0;
	tlvc->len_l 	= 0;
	tlvc->crc8 		= 0;
	tlvc->crc16		= 0;
	tlvc->total_len	= 0;
	tlvc->value_len	= 0;
    tlvc->elem_num = 0;
    tlvc->elems    = NULL;	
}

int32_t protocol_tlvc_decode_value(protocol_tlvc_t *tlvc,
									uint8_t *din, 
									uint32_t lin, 
									uint8_t *dout, 
									uint32_t lout)
{	
	protocol_tlvc_reset(tlvc);

	uint32_t used_bytes = 0;
	for (uint32_t offset = 0; offset < lin; offset += used_bytes)
	{
		used_bytes = 1;
		//mlog_i("tlvc", "state:%c, %d/%d", tlvc->state, lin, lin);
		switch (tlvc->state)
		{	
		case '0': // head0
			{
				if (din[offset] == tlvc->head0)
				{
					tlvc->state = '1';				
				}			
			} break;			
		case '1': // head1
			{
				if (din[offset] == tlvc->head1)
				{
					tlvc->state = 'L';
					tlvc->index= 0 ;
				}
				else
				{
					tlvc->state = '0';	
				}
			} break;			
		case 'L': // length
			{
				if(tlvc->index == 0)
				{
					tlvc->len_l = din[offset];
					tlvc->index = 1;
				}
				else if(tlvc->index == 1)
				{
					tlvc->len_h = din[offset];
					tlvc->state = '8';
				}
			} break;
		case '8': // head+length crc8
			{
				uint8_t crc8_data[4]={tlvc->head0, tlvc->head1, tlvc->len_l, tlvc->len_h};
				tlvc->crc8 = crc8(crc8_data,4);
				if (tlvc->crc8 == din[offset])
				{
					tlvc->state = 'V';	 
					tlvc->total_len = (tlvc->len_h << 8)+ tlvc->len_l;
					tlvc->value_len = tlvc->total_len - 7;
					tlvc->buffer.bytes = 0;
				}
				else
				{
					tlvc->state = '0';	
				}
			} break;		
		case 'V':  // value
			{
				uint32_t data_size = lin - offset;
				if (data_size < tlvc->value_len)
				{
					mlog_int_e("tlvc", 200, "data size %d < left %d, %d",
						data_size, tlvc->value_len, __LINE__);
					tlvc->state = '0'; 
					return -1;
				}

				memcpy(tlvc->buffer.data, (din + offset), tlvc->value_len);				
				tlvc->buffer.bytes = tlvc->value_len;
				tlvc->state = 'C';		
				tlvc->index = 0 ;				
				used_bytes = tlvc->value_len;

			} break;	
		case 'C': // CRC16 
			{						
				if(tlvc->index == 0)
				{
					tlvc->crc16 = (uint16_t)(din[offset]);
					tlvc->index = 1;
				}
				else if(tlvc->index == 1)
				{
					tlvc->state = '0';
					tlvc->crc16 |= (uint16_t)(din[offset]) << 8;
					uint16_t crc16_local = crc16(tlvc->buffer.data, tlvc->value_len);
					if (crc16_local != tlvc->crc16)
					{
						// crc16 error !
						mlog_int_e("tlvc", 100, "crc16 failed:%d-%d", crc16_local, tlvc->crc16);
					}
					else
					{
						if (lout != tlvc->value_len)
						{
							//mlog_int_e("tlvc", 100, "lout:%d != tlv value len:%d", lout, tlvc->value_len);
							//break;
							memcpy(dout, tlvc->buffer.data, lout);
							return 0;
						}
						else
						{
							memcpy(dout, tlvc->buffer.data, tlvc->value_len);							
							return 0;
						}
					}					
				}
			} break;					
		default :break;
		}
		if (used_bytes == 0)
		{
			used_bytes = 1;
		}
	}

    return -1;
}

int32_t protocol_tlvc_decode_tag(protocol_tlvc_t *tlvc,
									uint8_t *din,
									uint32_t lin,
									char *tag,
									uint32_t taglen,
									uint8_t *dout,
									uint32_t *lout)
{
	protocol_tlvc_reset(tlvc);

	uint32_t used_bytes = 0;
	for (uint32_t offset = 0; offset < lin; offset += used_bytes)
	{
		used_bytes = 1;
		//mlog_i("tlvc", "state:%c, %d/%d", tlvc->state, lin, lin);
		switch (tlvc->state)
		{
		case '0': // head0
			{
				if (din[offset] == tlvc->head0)
				{
					tlvc->state = '1';
				}
			} break;
		case '1': // head1
			{
				if (din[offset] == tlvc->head1)
				{
					tlvc->state = 'L';
					tlvc->index= 0 ;
				}
				else
				{
					tlvc->state = '0';
				}
			} break;
		case 'L': // length
			{
				if(tlvc->index == 0)
				{
					tlvc->len_l = din[offset];
					tlvc->index = 1;
				}
				else if(tlvc->index == 1)
				{
					tlvc->len_h = din[offset];
					tlvc->state = '8';
				}
			} break;
		case '8': // head+length crc8
			{
				uint8_t crc8_data[4]={tlvc->head0, tlvc->head1, tlvc->len_l, tlvc->len_h};
				tlvc->crc8 = crc8(crc8_data,4);
				if (tlvc->crc8 == din[offset])
				{
					tlvc->state = 'V';
					tlvc->total_len = (tlvc->len_h << 8)+ tlvc->len_l;
					tlvc->value_len = tlvc->total_len - 7;
					tlvc->buffer.bytes = 0;
				}
				else
				{
					tlvc->state = '0';
				}
			} break;
		case 'V':  // value
			{
				uint32_t data_size = lin - offset;
				if (data_size < tlvc->value_len)
				{
					mlog_int_e("tlvc", 200, "data size %d < left %d, %d",
						data_size, tlvc->value_len, __LINE__);
					tlvc->state = '0';
					return -1;
				}

				memcpy(tlvc->buffer.data, (din + offset), tlvc->value_len);
				tlvc->buffer.bytes = tlvc->value_len;
				tlvc->state = 'C';
				tlvc->index = 0 ;
				used_bytes = tlvc->value_len;

			} break;
		case 'C': // CRC16
			{
				if(tlvc->index == 0)
				{
					tlvc->crc16 = (uint16_t)(din[offset]);
					tlvc->index = 1;
				}
				else if(tlvc->index == 1)
				{
					tlvc->state = '0';
					tlvc->crc16 |= (uint16_t)(din[offset]) << 8;
					uint16_t crc16_local = crc16(tlvc->buffer.data, tlvc->value_len);
					if (crc16_local != tlvc->crc16)
					{
						// crc16 error !
						mlog_int_e("tlvc", 100, "crc16 failed:%d-%d", crc16_local, tlvc->crc16);
					}
					else
					{
						uint32_t in_len = *lout;
						uint8_t *val_ptr = tlvc->buffer.data + taglen + 1;
						uint32_t val_len = tlvc->buffer.bytes - taglen - 1;
						*lout = val_len;
						memcpy(dout, val_ptr, val_len);
						return 0;
					}
				}
			} break;
		default :break;
		}
		if (used_bytes == 0)
		{
			used_bytes = 1;
		}
	}

    return -1;
}
