#ifndef _GATT_SERVER_MULTI_CONNECT_H
#define _GATT_SERVER_MULTI_CONNECT_H

#include <stdint.h>
#include <stdbool.h>

typedef void (*gatt_write_call)(uint8_t , uint8_t *, uint32_t );
typedef void (*gatt_read_call)(uint8_t , uint8_t *, uint32_t *);
typedef void (*gatt_connect_call)(uint8_t , uint8_t);

#ifdef __cplusplus
extern "C" {
#endif

int32_t gatt_server_init(uint8_t *chip_id, uint8_t len);
// 0 phone, 1 charging base
void gatt_server_notify(uint8_t id, uint8_t *data, uint32_t len);
void gatt_server_regist_write_call(uint8_t id, gatt_write_call p);
void gatt_server_regist_connect_call(uint8_t id, gatt_connect_call p);
void gatt_server_prepare_read(uint8_t id, uint8_t *data, uint32_t len);
void gatt_server_ble_adv_start(void);
 
#ifdef __cplusplus
}
#endif

#endif /* _GATT_SERVER_H */
