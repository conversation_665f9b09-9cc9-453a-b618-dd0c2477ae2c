#ifndef _H_TYPE_BATTERY_A01_H_
#define _H_TYPE_BATTERY_A01_H_

#include <stdint.h>
 
typedef struct
{
    uint8_t date_time[4];
    uint16_t serial_number;
    uint16_t battery_voltage;
    int16_t  battery_current;
    int16_t  average_current;
    float    battery_temperature;
    uint16_t at_rate_time_to_full;
    uint16_t at_rate_time_to_empty;
    uint16_t max_error;
    uint16_t relative_state_of_charge;
    uint16_t absolute_state_of_charge;
    uint16_t remaining_capacity;
    uint16_t full_charge_capacity;
    uint16_t run_time_to_empty;
    uint16_t average_time_to_empty;
    uint16_t average_time_to_full;
    uint16_t charging_current;
    uint16_t charging_voltage;
    uint16_t cycle_count;
    uint16_t design_capacity;
    uint16_t design_voltage;
    uint16_t state_of_health;
} battery_info_driver_t;

typedef struct
{
	float 		voltage;
    float 		current;
    float 		temperature;
    float 		charge_time;
    float 		left_time;  
    uint8_t 	capacity_percent;		    
    uint16_t 	cycle_count;                
    uint16_t 	health_percent;            
} battery_info_user_t;

#endif
