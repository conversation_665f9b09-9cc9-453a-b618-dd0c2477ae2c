#include "protocol_tlvc_encrypt.h"
#include <string.h>
#include <stdlib.h>
#include "logger.h"
#include "crc.h"
#include "esp_heap_caps.h"

#define	TAG	 "tlvc_enc"

int32_t protocol_tlvc_encrypt_init(protocol_tlvc_enc_t *tlvc,
									uint8_t enable_encrypt,
									const uint8_t *key, 
									uint8_t keylen,
									pfunc_generate_iv pfunc_iv)
{
	tlvc->enc_enable = enable_encrypt;
	if (tlvc->enc_enable)
	{
		// encrypt parameter 
		if (keylen <= ENCRYPT_MAX_KEY_LENGTH)
		{
			tlvc->encrypt.key_length = keylen;
			memcpy(tlvc->encrypt.key, key, keylen);
		}
		if (pfunc_iv)
		{
			tlvc->encrypt.generate_iv = pfunc_iv;
		}

		tlvc->encrypt.generate_iv(tlvc->encrypt.iv, &tlvc->encrypt.iv_length);
	}
	else
	{
		tlvc->encrypt.key_length 		= 0;
		tlvc->encrypt.iv_length 		= 0;
		tlvc->encrypt.decrypt_iv_len 	= 0;
	}
	return 0;
}

int32_t protocol_tlvc_encrypt_string_regist(protocol_tlvc_enc_t *tlvc, 
										const char head0,
										const char head1,
										element_str_tlvc_t elems[], 
										uint32_t elemsize, 
										uint8_t* buf, 
										uint32_t buflen)
{    
	// state
	tlvc->state 	= '0';
	tlvc->head0 	= head0;
	tlvc->head1 	= head1;
	tlvc->len_h 	= 0;
	tlvc->len_l 	= 0;
	tlvc->crc8 		= 0;
	tlvc->crc16		= 0;
	tlvc->total_len	= 0;
	tlvc->value_len	= 0;

	// data buffer
	tlvc->buffer.data	= buf;
	tlvc->buffer.bytes 	= 0;
	tlvc->buffer.length = buflen;
	//mlog_i(TAG, "regist, buffer len = %d", tlvc->buffer.length);

	// elements
	tlvc->elem_num = elemsize;

	uint32_t size = elemsize * sizeof(element_str_tlvc_t);
	tlvc->elems    = (element_str_tlvc_t*)heap_caps_malloc(size, MALLOC_CAP_SPIRAM);
	if (tlvc->elems == NULL)
	{
		// Try to malloc internal ram
		tlvc->elems  = (element_str_tlvc_t*)heap_caps_malloc(size, MALLOC_CAP_INTERNAL);
		if (tlvc->elems == NULL)
	    {
			return -1;
		}
	}

	for (uint32_t i = 0; i < tlvc->elem_num; i++)
	{
		tlvc->elems[i].tagstr	= elems[i].tagstr;
		tlvc->elems[i].taglen  	= elems[i].taglen;
		tlvc->elems[i].pfunc   	= elems[i].pfunc;
	}

	return 0;
}

int32_t protocol_tlvc_encrypt_string_regist_malloc(protocol_tlvc_enc_t *tlvc, 
											const char head0,
											const char head1,
											element_str_tlvc_t elems[], 
											uint32_t elemsize, 
											uint8_t* buf, 
											uint32_t buflen)
{    
	// state
	tlvc->state 	= '0';
	tlvc->head0 	= head0;
	tlvc->head1 	= head1;
	tlvc->len_h 	= 0;
	tlvc->len_l 	= 0;
	tlvc->crc8 		= 0;
	tlvc->crc16		= 0;
	tlvc->total_len	= 0;
	tlvc->value_len	= 0;

	// data buffer
	tlvc->buffer.data	= buf;
	tlvc->buffer.bytes 	= 0;
	tlvc->buffer.length = buflen;
	//mlog_i(TAG, "regist, buffer len = %d", tlvc->buffer.length);

	// elements
	tlvc->elem_num = elemsize;

	uint32_t size = elemsize * sizeof(element_str_tlvc_t);
	tlvc->elems    = (element_str_tlvc_t*)malloc(size);
	if (tlvc->elems == NULL)
	{
	return -1;
	}

	for (uint32_t i = 0; i < tlvc->elem_num; i++)
	{
	tlvc->elems[i].tagstr	= elems[i].tagstr;
	tlvc->elems[i].taglen  	= elems[i].taglen;
	tlvc->elems[i].pfunc   	= elems[i].pfunc;
	}

	return 0;
}

int32_t protocol_tlvc_encrypt_cmdmap_regist(protocol_tlvc_enc_t *tlvc, 
											const char head0,
											const char head1,
											element_enum_tlvc_t cmdmap[], 
											uint32_t cmdnum, 
											uint8_t* buf, 
											uint32_t buflen)
{
	// state
	tlvc->state 	= '0';
	tlvc->head0 	= head0;
	tlvc->head1 	= head1;
	tlvc->len_h 	= 0;
	tlvc->len_l 	= 0;
	tlvc->crc8 		= 0;
	tlvc->crc16		= 0;
	tlvc->total_len	= 0;
	tlvc->value_len	= 0;

	// data buffer
	tlvc->buffer.data	= buf;
	tlvc->buffer.bytes 	= 0;
	tlvc->buffer.length = buflen;
	//mlog_i(TAG, "regist, buffer len = %d", tlvc->buffer.length);

	// elements
	tlvc->cmdnum 	= cmdnum;
	uint32_t size 	= cmdnum * sizeof(element_enum_tlvc_t);
	tlvc->cmdmap    = (element_enum_tlvc_t*)heap_caps_malloc(size, MALLOC_CAP_SPIRAM);
	if (tlvc->cmdmap == NULL)
	{
		return -1;
	}

	memset(tlvc->maptbl, 0, 0XFF);
	for (uint8_t i = 0; i < tlvc->cmdnum; i++)
	{
		tlvc->maptbl[cmdmap[i].tagenum] = i;
		tlvc->cmdmap[i].tagenum = cmdmap[i].tagenum;
		if (cmdmap[i].pfunc != NULL)
		{
			tlvc->cmdmap[i].pfunc   = cmdmap[i].pfunc;
		}
	}
	return 0;
}

int32_t protocol_tlvc_encrypt_cmdmap_regist_malloc(protocol_tlvc_enc_t *tlvc, 
											const char head0,
											const char head1,
											element_enum_tlvc_t cmdmap[], 
											uint32_t cmdnum, 
											uint8_t* buf, 
											uint32_t buflen)
{
	// state
	tlvc->state 	= '0';
	tlvc->head0 	= head0;
	tlvc->head1 	= head1;
	tlvc->len_h 	= 0;
	tlvc->len_l 	= 0;
	tlvc->crc8 		= 0;
	tlvc->crc16		= 0;
	tlvc->total_len	= 0;
	tlvc->value_len	= 0;

	// data buffer
	tlvc->buffer.data	= buf;
	tlvc->buffer.bytes 	= 0;
	tlvc->buffer.length = buflen;
	//mlog_i(TAG, "regist, buffer len = %d", tlvc->buffer.length);

	// elements
	tlvc->cmdnum 	= cmdnum;
	uint32_t size 	= cmdnum * sizeof(element_enum_tlvc_t);
	tlvc->cmdmap    = (element_enum_tlvc_t*)malloc(size);
	if (tlvc->cmdmap == NULL)
	{
		return -1;
	}

	memset(tlvc->maptbl, 0, 0XFF);
	for (uint8_t i = 0; i < tlvc->cmdnum; i++)
	{
		tlvc->maptbl[cmdmap[i].tagenum] = i;
		tlvc->cmdmap[i].tagenum = cmdmap[i].tagenum;
		if (cmdmap[i].pfunc != NULL)
		{
			tlvc->cmdmap[i].pfunc   = cmdmap[i].pfunc;
		}
	}
	return 0;
}

static int32_t protocol_tlvc_elems_decoding(protocol_tlvc_enc_t *tlvc)
{
	if (tlvc->buffer.data[0] == 1)
	{
		uint8_t cmd_enum  = tlvc->buffer.data[1];
		uint8_t cmd_index = tlvc->maptbl[cmd_enum];
		if (cmd_index < tlvc->cmdnum)
		{
			uint32_t val_len  		= tlvc->buffer.bytes - 2;
			uint32_t clear_offset 	= tlvc->buffer.bytes;
			uint32_t clear_len 		= tlvc->buffer.length - val_len - 2;
			memset(&tlvc->buffer.data[clear_offset], 0, clear_len);
			// mlog_i(TAG, "cmd_enum = 0x%02X, cmd_index = 0x%02X\r\n", cmd_enum, cmd_index);
			tlvc->cmdmap[cmd_index].pfunc(&tlvc->buffer.data[2], val_len);
		}
		else
		{
			mlog_int_e(TAG, 100, "cmd_index is invalid %X-%X!!!\r\n", cmd_index, tlvc->cmdnum);
		}
	}
	else
	{
		for (uint8_t i = 0; i < tlvc->elem_num; i++)
		{
			if (strncmp(tlvc->elems[i].tagstr,
						(const char*)(tlvc->buffer.data + 1),
						tlvc->elems[i].taglen) == 0)
			{
				uint8_t *val_ptr = tlvc->buffer.data + tlvc->elems[i].taglen + 1;
				uint32_t val_len = tlvc->buffer.bytes - tlvc->elems[i].taglen - 1;
				uint32_t clear_len = tlvc->buffer.length - val_len - tlvc->elems[i].taglen - 1;
				uint32_t clear_offset 	=  tlvc->buffer.bytes;
				memset(&tlvc->buffer.data[clear_offset], 0, clear_len);
				tlvc->elems[i].pfunc(val_ptr, val_len);
				break;
			}
		}
	}
	return 0;
}

static int32_t protocol_tlvc_value_encoding(protocol_tlvc_enc_t *tlvc, uint8_t *value, uint32_t length)
{
	for (uint16_t i = 0; i < length; i++)
	{
		value[i] ^= tlvc->encrypt.key[i % tlvc->encrypt.key_length];
		value[i] ^= tlvc->encrypt.iv[i % tlvc->encrypt.iv_length];
	}
	return 0;
}

static int32_t protocol_tlvc_value_decoding(protocol_tlvc_enc_t *tlvc, uint8_t *value, uint32_t length)
{
	for (uint16_t i = 0; i < length; i++)
	{
		value[i] ^= tlvc->encrypt.key[i % tlvc->encrypt.key_length];
		value[i] ^= tlvc->encrypt.decrypt_iv[i % tlvc->encrypt.decrypt_iv_len];
	}
#if 0
	printf("decrypt value_len = %d:\r\n", length);
	for (uint32_t i = 0; i < length; i++)
	{
		printf("%02X ", value[i]);
	}
	printf("\r\n");
#endif
	return 0;
}

static uint32_t protocol_tlvc_encrypt_push_byte(protocol_tlvc_enc_t *tlvc, uint32_t offset, uint8_t *data, uint32_t len)
{
    uint32_t used_bytes = 1;
    switch (tlvc->state)
    {
        case '0':
        	{
				if (data[offset] == tlvc->head0)
				{
					tlvc->state = '1';
				}
			} break;
        case '1':
        	{
				if (data[offset] == tlvc->head1)
				{
					tlvc->index = 0 ;
					tlvc->state = 'L';
				}
				else
				{
					tlvc->state = '0';
				}
			} break;

		case 'L': // iv_length + total_length
			{
				if(tlvc->index == 0)
				{
					tlvc->len_l = data[offset];
					tlvc->index = 1;
				}
				else if(tlvc->index == 1)
				{
					tlvc->len_h = data[offset];
					if (tlvc->enc_enable)
					{
						tlvc->encrypt.decrypt_iv_index = 0;
						tlvc->encrypt.decrypt_iv_len = (tlvc->len_h >> 4) & 0x0F;
						tlvc->state = 'I';
					}
					else
					{
						tlvc->encrypt.decrypt_iv_len = 0;
						tlvc->state = '8';
					}
				}
			} break;

        case 'I':
        	{
				if (tlvc->encrypt.decrypt_iv_index < tlvc->encrypt.decrypt_iv_len)
				{
					tlvc->encrypt.decrypt_iv[tlvc->encrypt.decrypt_iv_index++] = data[offset];
					if (tlvc->encrypt.decrypt_iv_index == tlvc->encrypt.decrypt_iv_len)
					{
						tlvc->state = '8';
					}
				}
				else
				{
					tlvc->state = '0';
				}
			} break;

        case '8': // head+length+iv crc8
        	{
				uint8_t local_crc8 = data[offset];
				uint8_t crc8_data[20];
				uint8_t crc8_len = 4;
				crc8_data[0] = tlvc->head0;
				crc8_data[1] = tlvc->head1;
				crc8_data[2] = tlvc->len_l;
				crc8_data[3] = tlvc->len_h;
				if (tlvc->enc_enable)
				{
					memcpy(&crc8_data[4], tlvc->encrypt.decrypt_iv, tlvc->encrypt.decrypt_iv_len);
					crc8_len += tlvc->encrypt.decrypt_iv_len;
				}
				tlvc->crc8 = crc8(crc8_data, crc8_len);
				if (tlvc->crc8 == local_crc8)
				{
					tlvc->len_h &= 0x0F;
					tlvc->total_len = (tlvc->len_h << 8) | tlvc->len_l;
					tlvc->value_len = tlvc->total_len - 7 - tlvc->encrypt.decrypt_iv_len;
					tlvc->buffer.bytes = 0;
					tlvc->state = 'V';
				}
				else
				{
					mlog_int_e(TAG, 100, "crc8 failed:%X-%X", local_crc8, tlvc->crc8);
					tlvc->state = '0';
				}
			} break;

			case 'V':  // value
			{
				uint32_t left_size = tlvc->value_len - tlvc->buffer.bytes;
				uint32_t data_size = len - offset;
				uint32_t copy_size = (left_size < data_size) ? (left_size) : (data_size);

				uint32_t buf_offset = tlvc->buffer.bytes;
				memcpy(tlvc->buffer.data + buf_offset, (data + offset), copy_size);

				tlvc->buffer.bytes += copy_size;
				if (tlvc->buffer.bytes == tlvc->value_len)
				{
					tlvc->state = 'C';
					tlvc->index = 0 ;
				}
				else if (tlvc->buffer.bytes > tlvc->value_len)
				{
					tlvc->state = '0'; // size wrong
				}
				// !!! not 1 byte
				used_bytes = copy_size;
            } break;

        case 'C':
        	{
				if(tlvc->index == 0)
				{
					tlvc->crc16 = (uint16_t)(data[offset]);
					tlvc->index = 1;
				}
				else if(tlvc->index == 1)
				{
					tlvc->crc16 |= (uint16_t)(data[offset]) << 8;
					uint16_t crc16_local = crc16(tlvc->buffer.data, tlvc->value_len);
					if (crc16_local != tlvc->crc16)
					{
						// crc16 error !
						mlog_int_e(TAG, 100, "crc16 failed:%X-%X", crc16_local, tlvc->crc16);
					}
					else // crc16 check ok
					{
						// decrypt cipper to plain
						if (tlvc->enc_enable)
						{
							protocol_tlvc_value_decoding(tlvc, tlvc->buffer.data, tlvc->value_len);
						}
						// plain decode
						protocol_tlvc_elems_decoding(tlvc);
					}
					tlvc->state = '0';
				}
			} break;

        default: break;
    }

    return (offset + used_bytes);
}

int32_t protocol_tlvc_encrypt_decode(protocol_tlvc_enc_t *tlvc, uint8_t *data, uint32_t len)
{
	uint32_t offset = 0;
	while (offset < len)
	{
		//mlog_i(TAG, "offset:%d [%x], len:%u, state:%c", offset, data[offset], len, tlvc->state);
		offset = protocol_tlvc_encrypt_push_byte(tlvc, offset, data, len);
	}
	return 0;
}
	 
int32_t protocol_tlvc_encrypt_pack(protocol_tlvc_enc_t *tlvc,
									const char head0,
									const char head1,	
									const char *tag,
									uint32_t taglen,
									uint8_t *value, 
									uint32_t valuelen,
									uint8_t *data,
									uint32_t datalen)
{	
	int32_t offset = 0;
	data[0] = head0;
	data[1] = head1;
 
	uint16_t pack_len = 0;

	// generate iv
	if (tlvc->enc_enable)
	{
		tlvc->encrypt.generate_iv(tlvc->encrypt.iv, &tlvc->encrypt.iv_length);
	}
	if (taglen == 0)
	{
		pack_len = 5 + tlvc->encrypt.iv_length + valuelen + 2;
	}
	else
	{
		pack_len = 5 + tlvc->encrypt.iv_length + 1 + taglen + valuelen + 2;
	}

	pack_len |= (uint16_t)tlvc->encrypt.iv_length << 12;

	memcpy(data + 2, &pack_len, 2);

	// pack iv
	uint8_t crc8_len = 4;
	if (tlvc->enc_enable)
	{
		memcpy(data + 4, tlvc->encrypt.iv, tlvc->encrypt.iv_length);
		crc8_len += tlvc->encrypt.iv_length;
	}

	uint8_t crc8_t = crc8(&data[0], crc8_len);
	data[crc8_len] = crc8_t;
 
 	offset = crc8_len + 1;
	uint32_t encrypt_start = offset;

 	if (taglen > 0)
 	{
		memcpy(data + offset, &taglen, 1);
		offset += 1;
		memcpy(data + offset, tag, taglen);
		offset += taglen;
 	}

 	if (valuelen > 0)
 	{
		memcpy(data + offset, value, valuelen);
		offset += valuelen;
	}

	// encryp tag && value
	if (tlvc->enc_enable)
	{
		protocol_tlvc_value_encoding(tlvc, &data[encrypt_start], (offset - encrypt_start));
    }
	else
	{
	 #if 0
		printf("encrypt value_len = %d:\r\n", offset - encrypt_start);
		for (uint32_t i = 0; i < (offset - encrypt_start); i++)
		{
			printf("%02X ", data[encrypt_start + i]);
		}
	 #endif
	}

	uint32_t crc_len = 0;
	if (taglen == 0)
	{
		crc_len = valuelen;
	}
	else
	{
		crc_len = (1 + taglen + valuelen);
	}
	uint16_t crc16_val = crc16(data + crc8_len + 1, crc_len);
	memcpy(data + offset, &crc16_val, 2);
	offset += 2;

#if 0
	printf("encrypt pack_len = %d:\r\n", offset);
	for (uint32_t i = 0; i < offset; i++)
	{
		printf("%02X ", data[i]);
	}
#endif
	return offset;
}

int32_t protocol_tlvc_encrypt_cmdmap_pack(protocol_tlvc_enc_t *tlvc,
										const char head0,
										const char head1,	
										uint8_t  cmdenum,
										uint32_t cmdlen,
										uint8_t *value, 
										uint32_t valuelen,
										uint8_t *data,
										uint32_t datalen)
{	

	int32_t offset = 0;
	data[0] = head0;
	data[1] = head1;

	uint16_t pack_len = 0;

	// generate iv
	if (tlvc->enc_enable)
	{
		tlvc->encrypt.generate_iv(tlvc->encrypt.iv, &tlvc->encrypt.iv_length);
	}
	if (cmdlen == 0)
	{
		pack_len = 5 + tlvc->encrypt.iv_length + valuelen + 2;
	}
	else
	{
		pack_len = 5 + tlvc->encrypt.iv_length + 1 + cmdlen + valuelen + 2;
	}

	pack_len |= (uint16_t)tlvc->encrypt.iv_length << 12;

	memcpy(data + 2, &pack_len, 2);

	// pack iv
	uint8_t crc8_len = 4;
	if (tlvc->enc_enable)
	{
		memcpy(data + 4, tlvc->encrypt.iv, tlvc->encrypt.iv_length);
		crc8_len += tlvc->encrypt.iv_length;
	}

	uint8_t crc8_t = crc8(&data[0], crc8_len);
	data[crc8_len] = crc8_t;

	offset = crc8_len + 1;
	uint32_t encrypt_start = offset;

	if (cmdlen > 0)
	{
		memcpy(data + offset, &cmdlen, 1);
		offset += 1;
		memcpy(data + offset, &cmdenum, cmdlen);
		offset += cmdlen;
	}

	if (valuelen > 0)
	{
		memcpy(data + offset, value, valuelen);
		offset += valuelen;
	}

	// encryp tag && value
	if (tlvc->enc_enable)
	{
		protocol_tlvc_value_encoding(tlvc, &data[encrypt_start], (offset - encrypt_start));
	}

	uint32_t crc_len = 0;
	if (cmdlen == 0)
	{
		crc_len = valuelen;
	}
	else
	{
		crc_len = (1 + cmdlen + valuelen);
	}
	uint16_t crc16_val = crc16(data + crc8_len + 1, crc_len);
	memcpy(data + offset, &crc16_val, 2);
	offset += 2;
	#if 0
	printf("encrypt pack_len = %d:\r\n", offset);
	for (uint32_t i = 0; i < offset; i++)
	{
		printf("%02X ", data[i]);
	}
	#endif

	return offset;
}

int32_t protocol_tlvc_encrypt_regist_value(protocol_tlvc_enc_t *tlvc, 
										const char head0,
										const char head1,
										uint8_t* buf, 
										uint32_t buflen)
{    
	// state
	tlvc->state 	= '0';
	tlvc->head0 	= head0;
	tlvc->head1 	= head1;
	tlvc->len_h 	= 0;
	tlvc->len_l 	= 0;
	tlvc->crc8 		= 0;
	tlvc->crc16		= 0;
	tlvc->total_len	= 0;
	tlvc->value_len	= 0;

	// data buffer
	tlvc->buffer.data	= buf;
	tlvc->buffer.bytes 	= 0;
	tlvc->buffer.length = buflen;
	return 0;
}

void protocol_tlvc_para_reset(protocol_tlvc_enc_t *tlvc)
{
	tlvc->state 	= '0';
	tlvc->len_h 	= 0;
	tlvc->len_l 	= 0;
	tlvc->crc8 		= 0;
	tlvc->crc16		= 0;
	tlvc->total_len	= 0;
	tlvc->value_len	= 0;
}

int32_t protocol_tlvc_encrypt_decode_value(protocol_tlvc_enc_t *tlvc,
											uint8_t *din, 
											uint32_t lin, 
											uint8_t *dout, 
											uint32_t *lout)
{	
	protocol_tlvc_para_reset(tlvc);

	uint32_t used_bytes = 0;
	for (uint32_t offset = 0; offset < lin; offset += used_bytes)
	{
		used_bytes = 1;
		// mlog_i(TAG, "state:%c, %d/%d", tlvc->state, lin, lin);
		switch (tlvc->state)
		{	
			case '0': // head0
				{
					if (din[offset] == tlvc->head0)
					{
						tlvc->state = '1';				
					}			
				} break;			
			case '1': // head1
				{
					if (din[offset] == tlvc->head1)
					{
						tlvc->index= 0 ;
						tlvc->state = 'L';
					}
					else
					{
						tlvc->state = '0';	
					}
				} break;			
			case 'L': // length
				{
					if(tlvc->index == 0)
					{
						tlvc->len_l = din[offset];
						tlvc->index = 1;
					}
					else if(tlvc->index == 1)
					{
						tlvc->len_h = din[offset];
						if (tlvc->enc_enable)
						{
							tlvc->encrypt.decrypt_iv_index = 0;
							tlvc->encrypt.decrypt_iv_len = (tlvc->len_h >> 4) & 0x0F;
							tlvc->state = 'I';
						}
						else
						{
							tlvc->encrypt.decrypt_iv_len = 0;
							tlvc->state = '8';
						}
					}
				} break;
			case 'I':
				if (tlvc->encrypt.decrypt_iv_index < tlvc->encrypt.decrypt_iv_len)
				{
					tlvc->encrypt.decrypt_iv[tlvc->encrypt.decrypt_iv_index++] = din[offset];
				}
				if (tlvc->encrypt.decrypt_iv_index == tlvc->encrypt.decrypt_iv_len) 
				{
					tlvc->state = '8';
				}
            break;
			case '8': // head+length crc8
				{
					uint8_t crc8_data[20];
					uint8_t crc8_len = 4;
					crc8_data[0] = tlvc->head0;
					crc8_data[1] = tlvc->head1;
					crc8_data[2] = tlvc->len_l;
					crc8_data[3] = tlvc->len_h;
					if (tlvc->enc_enable)
					{
						memcpy(&crc8_data[4], tlvc->encrypt.decrypt_iv, tlvc->encrypt.decrypt_iv_len);
						crc8_len += tlvc->encrypt.decrypt_iv_len;
					}
					tlvc->crc8 = crc8(crc8_data, crc8_len);
					if (tlvc->crc8 == din[offset])
					{
						tlvc->len_h &= 0x0F;
						tlvc->total_len = (tlvc->len_h << 8)+ tlvc->len_l;
						tlvc->value_len = tlvc->total_len - 7 - tlvc->encrypt.decrypt_iv_len;
						tlvc->buffer.bytes = 0;
						tlvc->state = 'V';
					}
					else
					{
						mlog_int_e(TAG, 100, "crc8 failed:%X-%X", din[offset], tlvc->crc8);
						tlvc->state = '0';	
					}
				} break;		
			case 'V':  // value
				{
					uint32_t data_size = lin - offset;
					if (data_size < tlvc->value_len)
					{
						mlog_int_e(TAG, 200, "data size %d < left %d, %d",
							data_size, tlvc->value_len, __LINE__);
						tlvc->state = '0'; 
						return -1;
					}

					memcpy(tlvc->buffer.data, (din + offset), tlvc->value_len);				
					tlvc->buffer.bytes = tlvc->value_len;
					tlvc->state = 'C';		
					tlvc->index = 0 ;				
					used_bytes = tlvc->value_len;

				} break;	
			case 'C': // CRC16 
				{						
					if(tlvc->index == 0)
					{
						tlvc->crc16 = (uint16_t)(din[offset]);
						tlvc->index = 1;
					}
					else if(tlvc->index == 1)
					{
						tlvc->state = '0';
						tlvc->crc16 |= (uint16_t)(din[offset]) << 8;
						uint16_t crc16_local = crc16(tlvc->buffer.data, tlvc->value_len);
						if (crc16_local != tlvc->crc16)
						{
							// crc16 error !
							mlog_int_e(TAG, 100, "crc16 failed:%d-%d", crc16_local, tlvc->crc16);
						}
						else
						{
							if (tlvc->enc_enable)
							{
								protocol_tlvc_value_decoding(tlvc, tlvc->buffer.data, tlvc->value_len);
						    }

						    uint32_t valuelen_in = *lout;
							if (valuelen_in < tlvc->value_len)
							{
								return -1;
							}

 							*lout = tlvc->value_len;
							memcpy(dout, tlvc->buffer.data, tlvc->value_len);
							return 0;
						}					
					}
				} break;					
			default :break;
		}
		if (used_bytes == 0)
		{
			used_bytes = 1;
		}
	}
    return -1;
}

int32_t protocol_tlvc_encrypt_reset(protocol_tlvc_enc_t *tlvc)
{
	tlvc->state 	= '0';
	tlvc->len_h 	= 0;
	tlvc->len_l 	= 0;
	tlvc->crc8 		= 0;
	tlvc->crc16		= 0;
	tlvc->total_len	= 0;
	tlvc->value_len	= 0;
    tlvc->elem_num = 0;
	tlvc->cmdnum = 0;

	if (tlvc->elems)
	{
		free(tlvc->elems);
		tlvc->elems    = NULL;
	}	

	if (tlvc->cmdmap)
	{
		free(tlvc->cmdmap);
		tlvc->cmdmap    = NULL;
	}

	memset(tlvc->encrypt.key, 0, ENCRYPT_MAX_KEY_LENGTH);
	memset(tlvc->encrypt.iv, 0, ENCRYPT_MAX_IV_LENGTH);
	memset(tlvc->encrypt.decrypt_iv, 0, ENCRYPT_MAX_IV_LENGTH);
	tlvc->encrypt.key_length = 0;
	tlvc->encrypt.iv_length = 0;
	tlvc->encrypt.decrypt_iv_len = 0;

	if (tlvc->encrypt.generate_iv)
	{
		tlvc->encrypt.generate_iv = NULL;
	}
    
	return 0;
}

