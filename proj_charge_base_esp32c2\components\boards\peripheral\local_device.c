#include "hardware_link.h"
#include "local_device.h"
#include "logger.h"

#define tag  "Dev_charge"
 
static device_t devlocal;


int32_t board_device_init(void)
{
#if 1    
    if (board_drv_link_timestamp(&devlocal.time) != 0)
    {
        mlog_e(tag, "driver failed: timestamp");
    }
#endif

#if 1
    if (board_drv_link_chip_id(&devlocal.chip_id) != 0)
    {
        mlog_e(tag, "driver failed: chipid");
    }
#endif

#if 1
    if (board_drv_link_nvs(&devlocal.nvs) != 0)
    {
        mlog_e(tag, "driver failed: nvs");
    }
#endif 

#if 1    
    if (board_drv_link_i2c0_bus(&devlocal.i2c0_bus) != 0)
    {
        mlog_e(tag, "driver failed: i2c0");
    }    
    if (board_drv_link_comport0(&devlocal.comport0) != 0)
    {
        mlog_e(tag, "driver failed: comport0");
    }
#endif
    if (board_drv_link_vbus_ctrl(&devlocal.gpio_power_ctrl) != 0)
    {
        mlog_e(tag, "driver failed: gpio_vbus_ctrl");
    }  

    if (board_drv_link_pixel_onboard(&devlocal.pixel_onboard) != 0)
    {
        mlog_e(tag, "driver failed: pixel_onboard");
    }

    if (board_drv_link_adc(&devlocal.vbus_volt) != 0)
    {
        mlog_e(tag, "driver failed: vbus_volt");
    }
    
    return 0;
}

const device_t* dev_charge()
{
    return &devlocal;
}

